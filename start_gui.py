#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HubStudio + YouTube 自动化上传工具启动器
使用MVC架构分离的新版GUI
"""

import sys
import os
from loguru import logger

def main():
    """启动GUI程序"""
    try:
        logger.info("🚀 启动HubStudio + YouTube自动化上传工具")
        logger.info("📋 使用MVC架构分离的新版GUI")
        
        # 导入新的GUI主程序（MVC架构）
        from gui_main_new import main as gui_main_new
        
        # 启动新架构的GUI
        gui_main_new()
        
    except ImportError as e:
        logger.error(f"❌ 导入模块失败: {e}")
        logger.info("请确保所有依赖已正确安装: pip install -r requirements.txt")
        input("按回车键退出...")
        
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
