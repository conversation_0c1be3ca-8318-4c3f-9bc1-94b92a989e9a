@echo off
chcp 65001 >nul
echo ========================================
echo HubStudio + YouTube 自动化上传工具
echo 命令行版本启动器
echo ========================================
echo.

echo 使用说明:
echo 1. 批量上传默认文件夹: 直接按回车
echo 2. 上传单个文件: 输入完整文件路径
echo 3. 上传指定文件夹: 输入文件夹路径
echo.

set /p input="请输入文件/文件夹路径（直接回车使用默认）: "

if "%input%"=="" (
    echo 使用默认视频文件夹进行批量上传...
    "HubStudio_YouTube_Uploader_CLI_Fixed.exe"
) else (
    echo 上传指定路径: %input%
    "HubStudio_YouTube_Uploader_CLI_Fixed.exe" "%input%"
)

echo.
echo 程序执行完成
pause
