# -*- mode: python ; coding: utf-8 -*-
"""
HubStudio + YouTube 自动化上传工具 - GUI版本
PyInstaller 配置文件
"""

import os
import sys
from pathlib import Path

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件列表 - 包含配置文件和资源目录
datas = [
    # 配置文件
    (os.path.join(project_root, 'config.ini'), '.'),
    
    # GUI模块目录
    (os.path.join(project_root, 'gui'), 'gui'),
    
    # 创建空的目录结构（运行时需要）
    (os.path.join(project_root, 'logs'), 'logs'),
    (os.path.join(project_root, 'screenshots'), 'screenshots'),
    (os.path.join(project_root, 'videos'), 'videos'),
]

# 隐藏导入 - 处理动态导入的模块
hiddenimports = [
    # GUI相关
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    
    # Selenium相关
    'selenium',
    'selenium.webdriver',
    'selenium.webdriver.chrome',
    'selenium.webdriver.chrome.options',
    'selenium.webdriver.chrome.service',
    'selenium.webdriver.common.by',
    'selenium.webdriver.support.ui',
    'selenium.webdriver.support.expected_conditions',
    'selenium.webdriver.common.keys',
    'selenium.webdriver.common.action_chains',
    'selenium.common.exceptions',
    
    # WebDriver Manager
    'webdriver_manager',
    'webdriver_manager.chrome',
    
    # OpenCV相关
    'cv2',
    'numpy',
    
    # 图像处理
    'PIL',
    'PIL.Image',
    
    # 日志系统
    'loguru',
    
    # 系统工具
    'psutil',
    'requests',
    'configparser',
    'pathlib',
    'queue',
    'threading',
    'concurrent.futures',
    'hashlib',
    'datetime',
    'time',
    'json',
    
    # 项目模块
    'browser_manager',
    'video_uploader_unified',
    'gui.gui_controller',
    'gui.gui_view',
    'gui.gui_view_simple',
    'main',
]

# 排除的模块
excludes = [
    'matplotlib',
    'scipy',
    'pandas',
    'jupyter',
    'notebook',
    'IPython',
]

# 分析配置
a = Analysis(
    ['start_gui.py'],  # 主入口文件
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=0,
)

# 处理重复文件
pyz = PYZ(a.pure)

# 可执行文件配置
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='HubStudio_YouTube_Uploader_GUI',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # GUI版本不显示控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
