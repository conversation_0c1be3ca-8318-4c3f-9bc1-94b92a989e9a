"""
GUI控制器 - 处理业务逻辑和数据管理
分离界面展示和业务逻辑
"""

import threading
import os
import configparser
import queue
import time
from datetime import datetime
import requests
import hashlib
import concurrent.futures
from pathlib import Path

# 导入自动化模块
from main import HubStudioYouTubeAutomation
from video_uploader_unified import VideoUploaderUnified


class GUIController:
    """GUI控制器类 - 负责业务逻辑处理"""
    
    def __init__(self):
        # 初始化配置
        self.config = configparser.ConfigParser()
        self.load_config()
        
        # 初始化状态变量
        self.automation = None
        self.video_uploader = None
        self.video_list = []
        self.is_uploading = False
        self.log_queue = queue.Queue()
        self.upload_results = {}
        
        # 回调函数（由GUI视图设置）
        self.callbacks = {
            'on_status_update': None,
            'on_progress_update': None,
            'on_log_message': None,
            'on_env_list_update': None,
            'on_video_count_update': None,
            'on_upload_finished': None
        }
    
    def set_callback(self, event_name, callback_func):
        """设置回调函数"""
        if event_name in self.callbacks:
            self.callbacks[event_name] = callback_func
    
    def _trigger_callback(self, event_name, *args, **kwargs):
        """触发回调函数"""
        callback = self.callbacks.get(event_name)
        if callback:
            callback(*args, **kwargs)
    
    # ==================== 配置管理 ====================
    
    def load_config(self):
        """加载配置文件"""
        try:
            # 处理PyInstaller打包后的路径问题
            import sys
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe文件
                base_path = sys._MEIPASS
                config_path = os.path.join(base_path, 'config.ini')
            else:
                # 如果是开发环境
                config_path = 'config.ini'
                
            if os.path.exists(config_path):
                self.config.read(config_path, encoding='utf-8')
                self._ensure_config_completeness()
            else:
                self.create_default_config()
        except Exception as e:
            self.log_message(f"加载配置文件失败: {e}", "ERROR")
    
    def _ensure_config_completeness(self):
        """确保配置完整性"""
        config_updated = False
        
        sections = {
            'BROWSER': {
                'api_url': 'http://localhost:6873',
                'readonly_mode': 'false',
                'headless_mode': 'false',
                'cdp_hide': 'true'
            },
            'HUBSTUDIO': {
                'api_id': '',
                'api_secret': ''
            },
            'VIDEO': {
                'video_folder': './videos',
                'default_title': '自动上传视频',
                'default_description': '通过HubStudio自动化脚本上传到YouTube的视频',
                'default_tags': '自动化,HubStudio,YouTube,视频上传'
            },
            'AUTOMATION': {
                'wait_timeout': '30',
                'upload_timeout': '600',
                'retry_count': '3',
                'upload_interval': '10'
            },
            'YOUTUBE': {
                'upload_url': 'https://studio.youtube.com'
            }
        }
        
        for section_name, section_data in sections.items():
            if not self.config.has_section(section_name):
                self.config.add_section(section_name)
                config_updated = True
            
            for key, default_value in section_data.items():
                if not self.config.has_option(section_name, key):
                    self.config.set(section_name, key, default_value)
                    config_updated = True
        
        if config_updated:
            self.save_config()
    
    def create_default_config(self):
        """创建默认配置"""
        self.config['BROWSER'] = {
            'api_url': 'http://localhost:6873',
            'readonly_mode': 'false',
            'headless_mode': 'false',
            'cdp_hide': 'true'
        }
        self.config['HUBSTUDIO'] = {
            'api_id': '',
            'api_secret': ''
        }
        self.config['YOUTUBE'] = {
            'upload_url': 'https://studio.youtube.com'
        }
        self.config['VIDEO'] = {
            'video_folder': './videos',
            'default_title': '自动上传视频',
            'default_description': '通过HubStudio自动化脚本上传到YouTube的视频',
            'default_tags': '自动化,HubStudio,YouTube,视频上传'
        }
        self.config['AUTOMATION'] = {
            'wait_timeout': '30',
            'upload_timeout': '600',
            'retry_count': '3',
            'upload_interval': '10'
        }
        self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 处理PyInstaller打包后的路径问题
            import sys
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe文件，保存到exe同目录
                exe_dir = os.path.dirname(sys.executable)
                config_path = os.path.join(exe_dir, 'config.ini')
            else:
                # 如果是开发环境
                config_path = 'config.ini'
                
            with open(config_path, 'w', encoding='utf-8') as f:
                self.config.write(f)
            return True
        except Exception as e:
            self.log_message(f"保存配置文件失败: {e}", "ERROR")
            return False
    
    def update_config(self, section, key, value):
        """更新配置项"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, value)
    
    def get_config(self, section, key, fallback=''):
        """获取配置项"""
        return self.config.get(section, key, fallback=fallback)
    
    # ==================== 日志管理 ====================
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}"
        self.log_queue.put(formatted_message)
        self._trigger_callback('on_log_message', formatted_message)
    
    def get_log_messages(self):
        """获取日志消息"""
        messages = []
        try:
            while True:
                message = self.log_queue.get_nowait()
                messages.append(message)
        except queue.Empty:
            pass
        return messages
    
    def clear_logs(self):
        """清空日志"""
        # 清空队列
        while not self.log_queue.empty():
            try:
                self.log_queue.get_nowait()
            except queue.Empty:
                break
    
    # ==================== 视频文件管理 ====================
    
    def add_video_file(self, file_path):
        """添加视频文件"""
        if file_path and os.path.exists(file_path):
            # 只保留一个视频文件
            self.video_list = [file_path]
            self.log_message(f"已选择视频文件: {os.path.basename(file_path)}", "INFO")
            self._trigger_callback('on_video_count_update', len(self.video_list), file_path)
            return True
        return False
    
    def clear_video_selection(self):
        """清除视频选择"""
        self.video_list = []
        self.log_message("已清除视频选择", "INFO")
        self._trigger_callback('on_video_count_update', 0, "")
    
    def get_video_list(self):
        """获取视频列表"""
        return self.video_list.copy()
    
    def get_video_count(self):
        """获取视频数量"""
        return len(self.video_list)
    
    # ==================== HubStudio环境管理 ====================
    
    def test_hubstudio_connection(self, api_url, api_id, api_secret):
        """测试HubStudio连接"""
        def test_in_thread():
            try:
                self.log_message("正在测试HubStudio连接...", "INFO")
                
                if not api_id or not api_secret:
                    self.log_message("❌ API凭证未配置，请先配置API ID和API Secret", "ERROR")
                    return
                
                # 准备认证参数
                timestamp = str(int(time.time() * 1000))
                request_data = {"current": 1, "size": 1}
                
                # 构建签名
                params_str = ""
                sign_string = api_id + timestamp + params_str
                signature = hashlib.sha256(sign_string.encode()).hexdigest()
                
                # 构建请求头
                headers = {
                    'Content-Type': 'application/json',
                    'api_key': api_id,
                    'req_time': timestamp,
                    'sign': signature
                }
                
                # 测试连接
                response = requests.post(f"{api_url}/api/v1/env/list",
                                       json=request_data,
                                       headers=headers,
                                       timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == 0:
                        self.log_message("✅ HubStudio API连接成功", "INFO")
                        # 自动刷新环境列表
                        self.refresh_environments(api_url, api_id, api_secret)
                    else:
                        self.log_message(f"❌ API认证失败: {result.get('msg')}", "ERROR")
                else:
                    self.log_message(f"❌ HubStudio连接失败: HTTP {response.status_code}", "ERROR")
                    
            except requests.exceptions.ConnectionError:
                self.log_message("❌ 连接被拒绝，请检查HubStudio是否已启动", "ERROR")
            except requests.exceptions.Timeout:
                self.log_message("❌ 连接超时，请检查网络和API地址", "ERROR")
            except Exception as e:
                self.log_message(f"❌ 连接测试异常: {e}", "ERROR")
        
        threading.Thread(target=test_in_thread, daemon=True).start()
    
    def refresh_environments(self, api_url, api_id, api_secret):
        """刷新HubStudio环境列表"""
        def refresh_in_thread():
            try:
                self.log_message("正在获取HubStudio环境列表...", "INFO")
                
                if not api_id or not api_secret:
                    self._trigger_callback('on_env_list_update', [], "请配置API凭证")
                    return
                
                # 获取环境列表
                env_list = self._fetch_environment_list(api_url, api_id, api_secret)
                
                if env_list:
                    available_count = sum(1 for env in env_list if "已关闭" in env)
                    status_msg = f"发现 {len(env_list)} 个环境 (可用: {available_count})"
                    self.log_message(f"发现 {len(env_list)} 个HubStudio环境，其中 {available_count} 个可用", "INFO")
                else:
                    status_msg = "未发现环境"
                    self.log_message("未发现任何HubStudio环境", "WARNING")
                
                self._trigger_callback('on_env_list_update', env_list, status_msg)
                
            except Exception as e:
                self.log_message(f"获取环境列表异常: {e}", "ERROR")
                self._trigger_callback('on_env_list_update', [], "连接异常")
        
        threading.Thread(target=refresh_in_thread, daemon=True).start()
    
    def _fetch_environment_list(self, api_url, api_id, api_secret):
        """获取环境列表的内部方法"""
        try:
            # 准备认证参数
            timestamp = str(int(time.time() * 1000))
            request_data = {"current": 1, "size": 200}
            
            # 构建签名
            params_str = ""
            sign_string = api_id + timestamp + params_str
            signature = hashlib.sha256(sign_string.encode()).hexdigest()
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'api_key': api_id,
                'req_time': timestamp,
                'sign': signature
            }
            
            # 获取环境列表
            response = requests.post(f"{api_url}/api/v1/env/list",
                                   json=request_data,
                                   headers=headers,
                                   timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    containers = result.get('data', {}).get('list', [])
                    
                    # 获取环境状态
                    status_data = self._fetch_environment_status(api_url, api_id, api_secret, containers)
                    
                    # 构建环境列表
                    env_list = []
                    for container in sorted(containers, key=lambda x: x.get('containerCode', 0)):
                        container_code = str(container.get('containerCode', ''))
                        container_name = container.get('containerName', '')
                        
                        # 获取状态
                        status = status_data.get(container_code, 3)
                        status_map = {0: '已开启', 1: '开启中', 2: '关闭中', 3: '已关闭'}
                        status_text = status_map.get(status, '未知')
                        
                        env_display = f"{container_code} - {container_name} - {status_text}"
                        env_list.append(env_display)
                    
                    return env_list
                else:
                    self.log_message(f"API调用失败: {result.get('msg', '未知错误')}", "ERROR")
            else:
                self.log_message(f"HubStudio连接失败: HTTP {response.status_code}", "ERROR")
                
        except requests.exceptions.ConnectionError:
            self.log_message("无法连接到HubStudio API", "ERROR")
        except requests.exceptions.Timeout:
            self.log_message("API请求超时，请检查网络连接", "ERROR")
        except Exception as e:
            self.log_message(f"获取环境列表失败: {e}", "ERROR")
        
        return []
    
    def _fetch_environment_status(self, api_url, api_id, api_secret, containers):
        """获取环境状态"""
        status_data = {}
        
        if not containers:
            return status_data
        
        try:
            container_codes = [str(container.get('containerCode', '')) for container in containers]
            
            # 构建状态查询的签名
            status_timestamp = str(int(time.time() * 1000))
            status_sign_string = api_id + status_timestamp + ""
            status_signature = hashlib.sha256(status_sign_string.encode()).hexdigest()
            
            status_headers = {
                'Content-Type': 'application/json',
                'api_key': api_id,
                'req_time': status_timestamp,
                'sign': status_signature
            }
            
            status_response = requests.post(f"{api_url}/api/v1/browser/all-browser-status",
                                          json={"containerCodes": container_codes},
                                          headers=status_headers,
                                          timeout=10)
            
            if status_response.status_code == 200:
                status_result = status_response.json()
                if status_result.get('code') == 0:
                    status_containers = status_result.get('data', {}).get('containers', [])
                    for status_container in status_containers:
                        code = status_container.get('containerCode', '')
                        status = status_container.get('status', 3)
                        status_data[str(code)] = status
                        
        except Exception as e:
            self.log_message(f"获取环境状态失败: {e}", "WARNING")
        
        return status_data
    
    def get_available_environments(self, api_url, api_id, api_secret):
        """获取可用环境列表"""
        env_list = self._fetch_environment_list(api_url, api_id, api_secret)
        # 只返回已关闭的环境（可以启动的环境）
        available_envs = [env for env in env_list if "已关闭" in env]
        return available_envs
    
    # ==================== 视频上传管理 ====================
    
    def start_upload(self, api_url, api_id, api_secret, concurrent_count, title, description, children_content):
        """开始上传视频"""
        if self.is_uploading:
            self.log_message("上传正在进行中...", "WARNING")
            return False
        
        if not self.video_list:
            self.log_message("请先添加视频文件", "ERROR")
            return False
        
        # 获取所有可用环境
        available_envs = self.get_available_environments(api_url, api_id, api_secret)
        if not available_envs:
            self.log_message("没有可用的HubStudio环境，请检查HubStudio连接", "ERROR")
            return False
        
        # 开始上传
        self.is_uploading = True
        self._trigger_callback('on_status_update', "🚀 正在初始化分批并发上传...")
        
        # 在新线程中执行分批并发上传
        upload_thread = threading.Thread(
            target=self._batch_concurrent_upload_thread,
            args=(available_envs, concurrent_count, title, description, children_content),
            daemon=True
        )
        upload_thread.start()
        
        return True
    
    def stop_upload(self):
        """停止上传"""
        if self.is_uploading:
            self.is_uploading = False
            self.log_message("正在停止所有并发上传任务...", "INFO")
            self._trigger_callback('on_status_update', "⏹️ 正在停止上传...")
    
    def _batch_concurrent_upload_thread(self, all_envs, concurrent_count, title_template, description_template, children_content):
        """分批并发上传视频的线程函数"""
        try:
            total_envs = len(all_envs)
            video_count = len(self.video_list)
            
            self.log_message(f"分批并发上传模式", "INFO")
            self.log_message(f"总环境数量: {total_envs} 个", "INFO")
            self.log_message(f"每批并发数量: {concurrent_count} 个", "INFO")
            self.log_message(f"视频文件数量: {video_count} 个", "INFO")
            
            # 计算总批次数
            total_batches = (total_envs + concurrent_count - 1) // concurrent_count
            self.log_message(f"总批次数: {total_batches} 批", "INFO")
            
            # 创建结果统计
            self.upload_results = {
                'total': total_envs,  # 总数是环境数量
                'completed': 0,
                'success': 0,
                'failed': 0,
                'lock': threading.Lock()
            }
            
            # 分批处理环境
            for batch_index in range(total_batches):
                if not self.is_uploading:
                    self.log_message("检测到停止信号，终止分批上传", "INFO")
                    break
                
                # 计算当前批次的环境范围
                start_idx = batch_index * concurrent_count
                end_idx = min(start_idx + concurrent_count, total_envs)
                current_batch_envs = all_envs[start_idx:end_idx]
                current_batch_size = len(current_batch_envs)
                
                self.log_message(f"\n开始第 {batch_index + 1}/{total_batches} 批次上传", "INFO")
                self.log_message(f"当前批次环境: {[env.split(' - ')[0] for env in current_batch_envs]}", "INFO")
                
                # 记录批次开始前的完成数量
                batch_start_completed = self.upload_results['completed']
                
                # 更新状态
                self._trigger_callback('on_status_update', 
                    f"批次 {batch_index + 1}/{total_batches} - 处理 {current_batch_size} 个环境")
                
                # 当前批次的并发上传
                if video_count == 1:
                    # 单个视频：当前批次的所有环境上传同一个视频
                    video_path = self.video_list[0]
                    self.log_message(f"单视频模式：当前批次 {current_batch_size} 个环境上传: {os.path.basename(video_path)}", "INFO")
                    
                    with concurrent.futures.ThreadPoolExecutor(max_workers=current_batch_size) as executor:
                        futures = []
                        for i, env in enumerate(current_batch_envs):
                            worker_id = start_idx + i + 1
                            future = executor.submit(
                                self._single_env_upload_worker,
                                env, video_path, title_template, description_template, children_content, worker_id
                            )
                            futures.append(future)
                        
                        # 等待当前批次完成
                        concurrent.futures.wait(futures)
                        
                        # 检查当前批次是否有任何环境成功启动
                        batch_completed = self.upload_results['completed'] - batch_start_completed
                        if batch_completed == 0:
                            self.log_message(f"❌ 批次 {batch_index + 1} 中没有任何环境成功启动，停止上传", "ERROR")
                            self.is_uploading = False
                            break
                        
                        # 检查当前批次是否有任何环境成功启动
                        batch_completed = self.upload_results['completed'] - batch_start_completed
                        if batch_completed == 0:
                            self.log_message(f"❌ 批次 {batch_index + 1} 中没有任何环境成功启动，停止上传", "ERROR")
                            self.is_uploading = False
                            break
                else:
                    # 多个视频：当前批次的环境处理视频队列
                    self.log_message(f"多视频模式：当前批次 {current_batch_size} 个环境处理视频队列", "INFO")
                    
                    # 创建当前批次的视频队列
                    batch_video_queue = queue.Queue()
                    for i, video_path in enumerate(self.video_list):
                        batch_video_queue.put((i, video_path))
                    
                    with concurrent.futures.ThreadPoolExecutor(max_workers=current_batch_size) as executor:
                        futures = []
                        for i, env in enumerate(current_batch_envs):
                            worker_id = start_idx + i + 1
                            future = executor.submit(
                                self._multi_video_upload_worker,
                                env, batch_video_queue, title_template, description_template, children_content, worker_id
                            )
                            futures.append(future)
                        
                        # 等待当前批次完成
                        concurrent.futures.wait(futures)
                        
                        # 检查是否有任何环境成功启动
                        if self.upload_results['completed'] == 0:
                            self.log_message(f"❌ 批次 {batch_index + 1} 中没有任何环境成功启动，停止上传", "ERROR")
                            self.is_uploading = False
                            break
                
                # 批次间隔（可配置）
                if batch_index < total_batches - 1 and self.is_uploading:
                    batch_interval = int(self.get_config('AUTOMATION', 'batch_interval', '10'))
                    self.log_message(f"批次 {batch_index + 1} 完成，等待 {batch_interval} 秒后开始下一批次", "INFO")
                    time.sleep(batch_interval)
            
            # 完成上传
            self._trigger_callback('on_progress_update', 100)
            total_tasks = self.upload_results['total']
            success_count = self.upload_results['success']
            failed_count = self.upload_results['failed']
            success_rate = (success_count / total_tasks) * 100 if total_tasks > 0 else 0
            
            self.log_message(f"\n分批并发上传完成！", "INFO")
            self.log_message(f"总环境数: {total_tasks}, 成功: {success_count}, 失败: {failed_count}", "INFO")
            self.log_message(f"成功率: {success_rate:.1f}%", "INFO")
            
            # 修复成功判断逻辑：只有当成功率大于50%时才认为整体成功
            overall_success = success_count > 0 and success_rate >= 50.0
            if success_count == 0:
                self.log_message("❌ 所有上传任务都失败了，请检查网络连接和YouTube登录状态", "ERROR")
            elif success_rate < 50.0:
                self.log_message(f"⚠️ 成功率较低({success_rate:.1f}%)，建议检查环境配置", "WARNING")
            else:
                self.log_message(f"✅ 上传任务完成，成功率: {success_rate:.1f}%", "INFO")
                
            self._trigger_callback('on_upload_finished', overall_success)
            
        except Exception as e:
            error_msg = f"分批并发上传过程中发生错误: {e}"
            self.log_message(error_msg, "ERROR")
            self._trigger_callback('on_upload_finished', False)
    
    def _single_env_upload_worker(self, env, video_path, title_template, description_template, children_content, worker_id):
        """单个环境上传视频的工作线程（单视频模式）"""
        automation = None
        try:
            if not self.is_uploading:
                self.log_message(f"工作线程 {worker_id} 检测到上传已停止，退出", "INFO")
                return
            
            # 提取环境ID
            profile_id = env.split(" - ")[0]
            env_name = env.split(" - ")[1] if " - " in env else profile_id
            
            self.log_message(f"🚀 工作线程 {worker_id} 启动，使用环境: {env_name} (ID: {profile_id})", "INFO")
            
            # 初始化自动化系统
            try:
                automation = HubStudioYouTubeAutomation('config.ini')
                if not automation:
                    raise Exception("自动化系统创建失败")
            except Exception as e:
                self.log_message(f"❌ 工作线程 {worker_id} 自动化系统创建失败: {e}", "ERROR")
                with self.upload_results['lock']:
                    self.upload_results['completed'] += 1
                    self.upload_results['failed'] += 1
                return

            try:
                init_success = automation.initialize(profile_id)
            except Exception as e:
                self.log_message(f"❌ 工作线程 {worker_id} 初始化异常: {e}", "ERROR")
                init_success = False

            if not init_success:
                self.log_message(f"❌ 工作线程 {worker_id} 环境启动失败 - 环境: {env_name} (可能是HubStudio连接问题或环境已被占用)", "ERROR")
                with self.upload_results['lock']:
                    self.upload_results['completed'] += 1
                    self.upload_results['failed'] += 1
                return
            
            self.log_message(f"✅ 工作线程 {worker_id} 初始化成功 - 环境: {env_name}", "INFO")
            
            # 安全获取视频文件名
            video_name = "未知视频"
            try:
                if video_path:
                    video_name = os.path.basename(video_path)
            except:
                pass

            # 准备上传参数
            try:
                title = title_template if title_template else (os.path.splitext(video_name)[0] if video_name != "未知视频" else "自动上传视频")
            except:
                title = "自动上传视频"

            description = description_template if description_template else "通过HubStudio自动化脚本上传到YouTube的视频"

            self.log_message(f"工作线程 {worker_id} 开始上传: {video_name}", "INFO")
            
            # 执行上传
            success = automation.upload_single_video(video_path, title, description, children_content)
            
            # 更新统计
            with self.upload_results['lock']:
                self.upload_results['completed'] += 1
                if success:
                    self.upload_results['success'] += 1
                    self.log_message(f"✅ 工作线程 {worker_id} 上传成功: {video_name}", "INFO")
                else:
                    self.upload_results['failed'] += 1
                    self.log_message(f"❌ 工作线程 {worker_id} 上传失败: {video_name}", "ERROR")
                
                # 更新进度
                progress = (self.upload_results['completed'] / self.upload_results['total']) * 100
                self._trigger_callback('on_progress_update', progress)
                self._trigger_callback('on_status_update', 
                    f"上传中... ({self.upload_results['completed']}/{self.upload_results['total']}) "
                    f"成功: {self.upload_results['success']}")
            
        except Exception as e:
            with self.upload_results['lock']:
                self.upload_results['completed'] += 1
                self.upload_results['failed'] += 1

            # 安全获取视频文件名
            video_name = "未知视频"
            try:
                if video_path:
                    video_name = os.path.basename(video_path)
            except:
                pass

            self.log_message(f"工作线程 {worker_id} 上传异常: {video_name} - {e}", "ERROR")
        finally:
            # 清理资源
            if automation:
                try:
                    automation.cleanup()
                except:
                    pass
            self.log_message(f"工作线程 {worker_id} 已清理资源", "INFO")
    
    def _multi_video_upload_worker(self, env, video_queue, title_template, description_template, children_content, worker_id):
        """多视频上传的工作线程（多视频模式）"""
        automation = None
        try:
            # 提取环境ID
            profile_id = env.split(" - ")[0]
            env_name = env.split(" - ")[1] if " - " in env else profile_id
            
            self.log_message(f"🚀 工作线程 {worker_id} 启动，使用环境: {env_name} (ID: {profile_id})", "INFO")
            
            # 初始化自动化系统
            try:
                automation = HubStudioYouTubeAutomation('config.ini')
                if not automation:
                    raise Exception("自动化系统创建失败")
            except Exception as e:
                self.log_message(f"❌ 工作线程 {worker_id} 自动化系统创建失败: {e}", "ERROR")
                return

            try:
                init_success = automation.initialize(profile_id)
            except Exception as e:
                self.log_message(f"❌ 工作线程 {worker_id} 初始化异常: {e}", "ERROR")
                init_success = False

            if not init_success:
                self.log_message(f"❌ 工作线程 {worker_id} 环境启动失败 - 环境: {env_name} (可能是HubStudio连接问题或环境已被占用)", "ERROR")
                return
            
            self.log_message(f"✅ 工作线程 {worker_id} 初始化成功，开始处理视频队列", "INFO")
            
            # 处理视频队列
            while self.is_uploading:
                try:
                    # 从队列获取视频任务，超时1秒
                    video_index, video_path = video_queue.get(timeout=1)
                except queue.Empty:
                    # 队列为空，检查是否还有任务
                    if video_queue.empty():
                        break
                    continue
                
                try:
                    # 安全获取视频文件名
                    video_name = "未知视频"
                    try:
                        if video_path:
                            video_name = os.path.basename(video_path)
                    except:
                        pass

                    # 生成标题（多视频时添加序号）
                    if title_template:
                        title = f"{title_template} - {video_index + 1:03d}"
                    else:
                        try:
                            title = os.path.splitext(video_name)[0] if video_name != "未知视频" else f"自动上传视频-{video_index + 1:03d}"
                        except:
                            title = f"自动上传视频-{video_index + 1:03d}"

                    description = description_template if description_template else "通过HubStudio自动化脚本上传到YouTube的视频"

                    self.log_message(f"工作线程 {worker_id} 开始上传: {video_name}", "INFO")
                    
                    # 执行上传
                    success = automation.upload_single_video(video_path, title, description, children_content)
                    
                    # 更新统计
                    with self.upload_results['lock']:
                        self.upload_results['completed'] += 1
                        if success:
                            self.upload_results['success'] += 1
                            self.log_message(f"✅ 工作线程 {worker_id} 上传成功: {video_name}", "INFO")
                        else:
                            self.upload_results['failed'] += 1
                            self.log_message(f"❌ 工作线程 {worker_id} 上传失败: {video_name}", "ERROR")
                        
                        # 更新进度
                        progress = (self.upload_results['completed'] / self.upload_results['total']) * 100
                        self._trigger_callback('on_progress_update', progress)
                        self._trigger_callback('on_status_update', 
                            f"上传中... ({self.upload_results['completed']}/{self.upload_results['total']}) "
                            f"成功: {self.upload_results['success']}")
                    
                    # 标记任务完成
                    video_queue.task_done()
                    
                    # 上传间隔
                    if self.is_uploading:
                        interval = int(self.get_config('AUTOMATION', 'upload_interval', '5'))
                        time.sleep(interval)
                        
                except Exception as e:
                    with self.upload_results['lock']:
                        self.upload_results['completed'] += 1
                        self.upload_results['failed'] += 1

                    # 安全获取视频文件名
                    video_name = "未知视频"
                    try:
                        if video_path:
                            video_name = os.path.basename(video_path)
                    except:
                        pass

                    self.log_message(f"工作线程 {worker_id} 上传异常: {video_name} - {e}", "ERROR")
                    # 标记任务完成
                    video_queue.task_done()
            
            self.log_message(f"工作线程 {worker_id} 完成所有任务", "INFO")
            
        except Exception as e:
            self.log_message(f"工作线程 {worker_id} 异常: {e}", "ERROR")
        finally:
            # 清理资源
            if automation:
                try:
                    automation.cleanup()
                except:
                    pass
            self.log_message(f"工作线程 {worker_id} 已清理资源", "INFO")
    
    # ==================== 状态管理 ====================
    
    def is_upload_in_progress(self):
        """检查是否正在上传"""
        return self.is_uploading
    
    def get_upload_results(self):
        """获取上传结果"""
        return self.upload_results.copy() if self.upload_results else {}