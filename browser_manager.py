"""
HubStudio 指纹浏览器管理器
专门用于管理HubStudio指纹浏览器的连接和操作
"""

import os
import time
import json
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from loguru import logger


class HubStudioManager:
    def __init__(self, config, profile_id=None):
        self.config = config
        self.profile_id = profile_id
        self.api_url = config.get('BROWSER', 'api_url', fallback='http://localhost:6873')
        self.driver = None
        self.browser_profile = None
        
    def get_environment_list(self):
        """获取HubStudio环境列表"""
        try:
            logger.info("获取HubStudio环境列表...")
            
            # 获取API凭证
            api_id = self.config.get('HUBSTUDIO', 'api_id', fallback='')
            api_secret = self.config.get('HUBSTUDIO', 'api_secret', fallback='')
            
            if not api_id or not api_secret:
                logger.error("API凭证未配置，请在配置文件中设置api_id和api_secret")
                return []
            
            import time
            import hashlib
            
            # 准备认证参数
            timestamp = str(int(time.time() * 1000))
            
            # 构建请求数据
            request_data = {
                "current": 1,
                "size": 200
            }
            
            # 构建签名（根据HubStudio文档：SHA256(apikey + timestamp + params)）
            params_str = ""
            sign_string = api_id + timestamp + params_str
            signature = hashlib.sha256(sign_string.encode()).hexdigest()
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'api_key': api_id,
                'req_time': timestamp,
                'sign': signature
            }
            
            logger.debug(f"使用API ID: {api_id[:10]}...")
            logger.debug(f"请求时间戳: {timestamp}")
            
            # 使用与GUI相同的API端点
            response = requests.post(f"{self.api_url}/api/v1/env/list",
                                   json=request_data,
                                   headers=headers,
                                   timeout=10)
            
            logger.debug(f"API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                logger.debug(f"API响应: {result}")
                
                if result.get('code') == 0:
                    containers = result.get('data', {}).get('list', [])
                    logger.info(f"获取到 {len(containers)} 个环境")
                    
                    # 转换为统一格式
                    profiles = []
                    for container in containers:
                        profile = {
                            'id': str(container.get('containerCode', '')),
                            'name': container.get('containerName', ''),
                            'status': 'closed',  # 默认状态
                            'browser_kernel': 'chrome'  # HubStudio环境默认支持Chrome
                        }
                        profiles.append(profile)
                    
                    logger.info(f"转换后有 {len(profiles)} 个可用环境")
                    return profiles
                else:
                    logger.error(f"获取环境列表失败: {result.get('msg')}")
                    
                    # 检查是否是认证问题
                    if result.get('code') == 401:
                        logger.error("API认证失败，请检查API ID和API Secret是否正确")
                    elif result.get('code') == 403:
                        logger.error("API权限不足，请检查API坐席是否可用")
                    
                    return []
            else:
                logger.error(f"API请求失败: {response.status_code}")
                
                if response.status_code == 401:
                    logger.error("认证失败，请检查API凭证是否正确")
                elif response.status_code == 403:
                    logger.error("权限不足，请检查API坐席是否可用")
                elif response.status_code == 404:
                    logger.error("API端点不存在，请检查API地址是否正确")
                
                try:
                    error_detail = response.json()
                    logger.debug(f"错误详情: {error_detail}")
                except:
                    logger.debug(f"响应内容: {response.text[:200]}")
                
                return []
                
        except requests.exceptions.ConnectionError:
            logger.error("无法连接到HubStudio API，请确保HubStudio客户端正在运行")
            return []
        except requests.exceptions.Timeout:
            logger.error("API请求超时，请检查网络连接")
            return []
        except Exception as e:
            logger.error(f"获取环境列表异常: {e}")
            import traceback
            logger.debug(f"详细错误: {traceback.format_exc()}")
            return []
    
    def start_browser_profile(self, profile_id=None):
        """启动指定的浏览器环境"""
        try:
            # 首先检查HubStudio连接
            if not self.check_hubstudio_connection():
                logger.error("❌ 无法连接到HubStudio API，请确保HubStudio客户端正在运行")
                return None

            if profile_id:
                self.profile_id = profile_id

            if not self.profile_id:
                # 如果没有指定profile_id，获取第一个可用的Chrome环境
                logger.info("🔍 获取可用的浏览器环境...")
                profiles = self.get_environment_list()
                if not profiles:
                    logger.error("❌ 没有可用的Chrome环境，请在HubStudio中创建浏览器环境")
                    return None

                self.profile_id = profiles[0]['id']
                logger.info(f"✅ 使用第一个可用环境: {profiles[0]['name']} (ID: {self.profile_id})")

            logger.info(f"🚀 启动浏览器环境: {self.profile_id}")

            # 调用HubStudio API启动浏览器
            start_url = f"{self.api_url}/api/v1/browser/start"
            start_data = {"containerCode": self.profile_id}

            # 添加API认证
            api_id = self.config.get('HUBSTUDIO', 'api_id', fallback='')
            api_secret = self.config.get('HUBSTUDIO', 'api_secret', fallback='')

            if not api_id or not api_secret:
                logger.error("❌ API认证信息未配置，请在config.ini中设置api_id和api_secret")
                return None

            import time
            import hashlib

            # 准备认证参数
            timestamp = str(int(time.time() * 1000))

            # 构建签名
            params_str = ""
            sign_string = api_id + timestamp + params_str
            signature = hashlib.sha256(sign_string.encode()).hexdigest()

            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'api_key': api_id,
                'req_time': timestamp,
                'sign': signature
            }

            logger.debug(f"使用API认证启动浏览器，API ID: {api_id[:10]}...")

            try:
                response = requests.post(start_url, json=start_data, headers=headers, timeout=30)
            except requests.exceptions.ConnectionError:
                logger.error("❌ 连接HubStudio API失败，请确保HubStudio客户端正在运行")
                return None
            except requests.exceptions.Timeout:
                logger.error("❌ HubStudio API请求超时")
                return None

            if response.status_code == 200:
                result = response.json()
                logger.debug(f"启动API响应: {result}")

                if result.get('code') == 0:
                    browser_data = result.get('data', {})
                    logger.debug(f"浏览器数据: {browser_data}")

                    # 尝试多种可能的调试端口字段名
                    debug_port = (browser_data.get('debuggingPort') or
                                browser_data.get('debug_port') or
                                browser_data.get('debugPort') or
                                browser_data.get('port') or
                                browser_data.get('debuggerPort'))

                    if debug_port:
                        logger.info(f"✅ 浏览器启动成功，调试端口: {debug_port}")

                        # 先保存浏览器数据，供_connect_to_browser使用
                        self.browser_profile = browser_data

                        # 连接到已启动的浏览器
                        driver = self._connect_to_browser(debug_port)
                        if driver:
                            self.driver = driver
                            logger.info("🎉 成功连接到HubStudio浏览器")
                            return driver
                        else:
                            logger.error("❌ 连接到浏览器失败")
                            # 尝试停止已启动的浏览器
                            self._stop_browser_by_api()
                            return None
                    else:
                        logger.error(f"❌ 未获取到调试端口，可用字段: {list(browser_data.keys())}")
                        return None
                else:
                    error_msg = result.get('msg', '未知错误')
                    logger.error(f"❌ 启动浏览器失败: {error_msg}")

                    # 根据错误代码提供具体建议
                    error_code = result.get('code')
                    if error_code == 401:
                        logger.error("💡 建议：检查API认证信息是否正确")
                    elif error_code == 403:
                        logger.error("💡 建议：检查API权限或坐席是否可用")
                    elif error_code == 404:
                        logger.error("💡 建议：检查浏览器环境ID是否存在")

                    return None
            else:
                logger.error(f"❌ 启动浏览器API请求失败: HTTP {response.status_code}")
                try:
                    error_detail = response.json()
                    logger.error(f"错误详情: {error_detail}")
                except:
                    logger.error(f"响应内容: {response.text[:200]}")
                return None

        except Exception as e:
            logger.error(f"❌ 启动浏览器环境异常: {e}")
            import traceback
            logger.debug(f"详细错误: {traceback.format_exc()}")
            return None
    
    def _connect_to_browser(self, debug_port):
        """连接到已启动的浏览器"""
        try:
            logger.info(f"🔗 连接到浏览器调试端口: {debug_port}")

            # 首先检查端口是否可用
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('127.0.0.1', int(debug_port)))
            sock.close()

            if result != 0:
                logger.error(f"❌ 调试端口 {debug_port} 不可访问")
                return None

            logger.info(f"✅ 调试端口 {debug_port} 可访问")

            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{debug_port}")

            # 添加更多选项以提高稳定性
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--remote-debugging-port=0")  # 让Chrome自动选择端口

            # 优先使用HubStudio自带的webdriver
            webdriver_path = None
            if self.browser_profile and 'webdriver' in self.browser_profile:
                webdriver_path = self.browser_profile['webdriver']
                logger.info(f"🔧 使用HubStudio自带的webdriver: {webdriver_path}")

            # 创建Service
            service = None
            if webdriver_path and os.path.exists(webdriver_path):
                try:
                    service = Service(webdriver_path)
                    logger.info("✅ 使用HubStudio webdriver")
                except Exception as e:
                    logger.warning(f"⚠️ HubStudio webdriver创建失败: {e}")
                    service = None

            if not service:
                try:
                    logger.info("🔧 使用系统ChromeDriver")
                    service = Service(ChromeDriverManager().install())
                except Exception as e:
                    logger.error(f"❌ 系统ChromeDriver创建失败: {e}")
                    return None

            # 创建WebDriver实例
            try:
                driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("✅ WebDriver实例创建成功")
            except Exception as e:
                logger.error(f"❌ WebDriver实例创建失败: {e}")
                return None

            # 测试连接 - 多次尝试
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    logger.info(f"🧪 测试浏览器连接 (尝试 {attempt + 1}/{max_attempts})")
                    driver.get("about:blank")

                    # 验证浏览器是否响应
                    title = driver.title
                    current_url = driver.current_url

                    logger.info(f"✅ 浏览器连接测试成功")
                    logger.info(f"   标题: {title}")
                    logger.info(f"   URL: {current_url}")

                    # 额外验证：尝试执行JavaScript
                    try:
                        result = driver.execute_script("return 'connection_test_ok';")
                        if result == 'connection_test_ok':
                            logger.info("✅ JavaScript执行测试成功")
                            return driver
                        else:
                            logger.warning("⚠️ JavaScript执行测试异常")
                    except Exception as js_e:
                        logger.warning(f"⚠️ JavaScript执行测试失败: {js_e}")
                        # 即使JS测试失败，如果基本连接成功也返回driver
                        return driver

                except Exception as e:
                    logger.warning(f"⚠️ 连接测试失败 (尝试 {attempt + 1}): {e}")
                    if attempt < max_attempts - 1:
                        time.sleep(2)  # 等待2秒后重试
                    else:
                        logger.error("❌ 所有连接测试都失败")
                        try:
                            driver.quit()
                        except:
                            pass
                        return None

            return None

        except Exception as e:
            logger.error(f"❌ 连接浏览器失败: {e}")
            import traceback
            logger.debug(f"详细错误: {traceback.format_exc()}")
            return None
    
    def stop_browser_profile(self):
        """停止浏览器环境"""
        try:
            if self.driver:
                logger.info("关闭WebDriver连接")
                self.driver.quit()
                self.driver = None
            
            if self.profile_id:
                logger.info(f"停止浏览器环境: {self.profile_id}")
                
                # 调用HubStudio API停止浏览器
                stop_url = f"{self.api_url}/api/v1/browser/stop"
                stop_data = {"containerCode": self.profile_id}
                response = requests.post(stop_url, json=stop_data, timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == 0:
                        logger.info("浏览器环境已停止")
                        return True
                    else:
                        logger.warning(f"停止浏览器失败: {result.get('msg')}")
                        return False
                else:
                    logger.warning(f"停止浏览器API请求失败: {response.status_code}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"停止浏览器环境异常: {e}")
            return False
    
    def get_browser_info(self):
        """获取当前浏览器信息"""
        if self.browser_profile:
            return {
                'profile_id': self.profile_id,
                'debug_port': self.browser_profile.get('debug_port'),
                'status': 'running' if self.driver else 'stopped'
            }
        return None
    
    def _stop_browser_by_api(self):
        """通过API停止浏览器（内部方法）"""
        try:
            if not self.profile_id:
                return False

            logger.info(f"🛑 尝试停止浏览器环境: {self.profile_id}")

            stop_url = f"{self.api_url}/api/v1/browser/stop"
            stop_data = {"containerCode": self.profile_id}

            # 添加API认证
            api_id = self.config.get('HUBSTUDIO', 'api_id', fallback='')
            api_secret = self.config.get('HUBSTUDIO', 'api_secret', fallback='')

            if api_id and api_secret:
                import time
                import hashlib

                timestamp = str(int(time.time() * 1000))
                params_str = ""
                sign_string = api_id + timestamp + params_str
                signature = hashlib.sha256(sign_string.encode()).hexdigest()

                headers = {
                    'Content-Type': 'application/json',
                    'api_key': api_id,
                    'req_time': timestamp,
                    'sign': signature
                }

                response = requests.post(stop_url, json=stop_data, headers=headers, timeout=10)
            else:
                response = requests.post(stop_url, json=stop_data, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    logger.info("✅ 浏览器环境已停止")
                    return True
                else:
                    logger.warning(f"⚠️ 停止浏览器失败: {result.get('msg')}")
            else:
                logger.warning(f"⚠️ 停止浏览器API请求失败: {response.status_code}")

            return False

        except Exception as e:
            logger.warning(f"⚠️ 停止浏览器异常: {e}")
            return False

    def check_hubstudio_connection(self):
        """检查HubStudio连接状态"""
        try:
            logger.debug("🔍 检查HubStudio连接状态...")
            response = requests.post(f"{self.api_url}/api/v1/env/list",
                                   json={"current": 1, "size": 1}, timeout=5)

            if response.status_code == 200:
                logger.debug("✅ HubStudio连接正常")
                return True
            else:
                logger.warning(f"⚠️ HubStudio连接异常: HTTP {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            logger.error("❌ 无法连接到HubStudio，请确保客户端正在运行")
            return False
        except requests.exceptions.Timeout:
            logger.error("❌ HubStudio连接超时")
            return False
        except Exception as e:
            logger.error(f"❌ 检查HubStudio连接失败: {e}")
            return False
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            if self.driver:
                self.driver.quit()
        except:
            pass
