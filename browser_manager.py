"""
HubStudio 指纹浏览器管理器
专门用于管理HubStudio指纹浏览器的连接和操作
"""

import os
import time
import json
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from loguru import logger


class HubStudioManager:
    def __init__(self, config, profile_id=None):
        self.config = config
        self.profile_id = profile_id
        self.api_url = config.get('BROWSER', 'api_url', fallback='http://localhost:6873')
        self.driver = None
        self.browser_profile = None
        
    def get_environment_list(self):
        """获取HubStudio环境列表"""
        try:
            logger.info("获取HubStudio环境列表...")
            
            # 获取API凭证
            api_id = self.config.get('HUBSTUDIO', 'api_id', fallback='')
            api_secret = self.config.get('HUBSTUDIO', 'api_secret', fallback='')
            
            if not api_id or not api_secret:
                logger.error("API凭证未配置，请在配置文件中设置api_id和api_secret")
                return []
            
            import time
            import hashlib
            
            # 准备认证参数
            timestamp = str(int(time.time() * 1000))
            
            # 构建请求数据
            request_data = {
                "current": 1,
                "size": 200
            }
            
            # 构建签名（根据HubStudio文档：SHA256(apikey + timestamp + params)）
            params_str = ""
            sign_string = api_id + timestamp + params_str
            signature = hashlib.sha256(sign_string.encode()).hexdigest()
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'api_key': api_id,
                'req_time': timestamp,
                'sign': signature
            }
            
            logger.debug(f"使用API ID: {api_id[:10]}...")
            logger.debug(f"请求时间戳: {timestamp}")
            
            # 使用与GUI相同的API端点
            response = requests.post(f"{self.api_url}/api/v1/env/list",
                                   json=request_data,
                                   headers=headers,
                                   timeout=10)
            
            logger.debug(f"API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                logger.debug(f"API响应: {result}")
                
                if result.get('code') == 0:
                    containers = result.get('data', {}).get('list', [])
                    logger.info(f"获取到 {len(containers)} 个环境")
                    
                    # 转换为统一格式
                    profiles = []
                    for container in containers:
                        profile = {
                            'id': str(container.get('containerCode', '')),
                            'name': container.get('containerName', ''),
                            'status': 'closed',  # 默认状态
                            'browser_kernel': 'chrome'  # HubStudio环境默认支持Chrome
                        }
                        profiles.append(profile)
                    
                    logger.info(f"转换后有 {len(profiles)} 个可用环境")
                    return profiles
                else:
                    logger.error(f"获取环境列表失败: {result.get('msg')}")
                    
                    # 检查是否是认证问题
                    if result.get('code') == 401:
                        logger.error("API认证失败，请检查API ID和API Secret是否正确")
                    elif result.get('code') == 403:
                        logger.error("API权限不足，请检查API坐席是否可用")
                    
                    return []
            else:
                logger.error(f"API请求失败: {response.status_code}")
                
                if response.status_code == 401:
                    logger.error("认证失败，请检查API凭证是否正确")
                elif response.status_code == 403:
                    logger.error("权限不足，请检查API坐席是否可用")
                elif response.status_code == 404:
                    logger.error("API端点不存在，请检查API地址是否正确")
                
                try:
                    error_detail = response.json()
                    logger.debug(f"错误详情: {error_detail}")
                except:
                    logger.debug(f"响应内容: {response.text[:200]}")
                
                return []
                
        except requests.exceptions.ConnectionError:
            logger.error("无法连接到HubStudio API，请确保HubStudio客户端正在运行")
            return []
        except requests.exceptions.Timeout:
            logger.error("API请求超时，请检查网络连接")
            return []
        except Exception as e:
            logger.error(f"获取环境列表异常: {e}")
            import traceback
            logger.debug(f"详细错误: {traceback.format_exc()}")
            return []
    
    def start_browser_profile(self, profile_id=None):
        """启动指定的浏览器环境"""
        try:
            if profile_id:
                self.profile_id = profile_id
            
            if not self.profile_id:
                # 如果没有指定profile_id，获取第一个可用的Chrome环境
                profiles = self.get_environment_list()
                if not profiles:
                    logger.error("没有可用的Chrome环境")
                    return None
                
                self.profile_id = profiles[0]['id']
                logger.info(f"使用第一个可用环境: {profiles[0]['name']} (ID: {self.profile_id})")
            
            logger.info(f"启动浏览器环境: {self.profile_id}")
            
            # 调用HubStudio API启动浏览器
            start_url = f"{self.api_url}/api/v1/browser/start"
            start_data = {"containerCode": self.profile_id}
            
            # 添加API认证
            api_id = self.config.get('HUBSTUDIO', 'api_id', fallback='')
            api_secret = self.config.get('HUBSTUDIO', 'api_secret', fallback='')
            
            if api_id and api_secret:
                import time
                import hashlib
                
                # 准备认证参数
                timestamp = str(int(time.time() * 1000))
                
                # 构建签名
                params_str = ""
                sign_string = api_id + timestamp + params_str
                signature = hashlib.sha256(sign_string.encode()).hexdigest()
                
                # 构建请求头
                headers = {
                    'Content-Type': 'application/json',
                    'api_key': api_id,
                    'req_time': timestamp,
                    'sign': signature
                }
                
                logger.debug(f"使用API认证启动浏览器，API ID: {api_id[:10]}...")
                response = requests.post(start_url, json=start_data, headers=headers, timeout=30)
            else:
                logger.warning("未配置API认证，使用无认证方式启动浏览器")
                response = requests.post(start_url, json=start_data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                logger.debug(f"启动API响应: {result}")
                
                if result.get('code') == 0:
                    browser_data = result.get('data', {})
                    logger.debug(f"浏览器数据: {browser_data}")
                    
                    # 尝试多种可能的调试端口字段名
                    debug_port = (browser_data.get('debuggingPort') or 
                                browser_data.get('debug_port') or 
                                browser_data.get('debugPort') or 
                                browser_data.get('port') or 
                                browser_data.get('debuggerPort'))
                    
                    if debug_port:
                        logger.info(f"浏览器启动成功，调试端口: {debug_port}")
                        
                        # 先保存浏览器数据，供_connect_to_browser使用
                        self.browser_profile = browser_data
                        
                        # 连接到已启动的浏览器
                        driver = self._connect_to_browser(debug_port)
                        if driver:
                            self.driver = driver
                            logger.info("成功连接到HubStudio浏览器")
                            return driver
                        else:
                            logger.error("连接到浏览器失败")
                            return None
                    else:
                        logger.error(f"未获取到调试端口，可用字段: {list(browser_data.keys())}")
                        return None
                else:
                    logger.error(f"启动浏览器失败: {result.get('msg')}")
                    return None
            else:
                logger.error(f"启动浏览器API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"启动浏览器环境异常: {e}")
            return None
    
    def _connect_to_browser(self, debug_port):
        """连接到已启动的浏览器"""
        try:
            logger.info(f"连接到浏览器调试端口: {debug_port}")
            
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{debug_port}")
            
            # 优先使用HubStudio自带的webdriver
            webdriver_path = None
            if self.browser_profile and 'webdriver' in self.browser_profile:
                webdriver_path = self.browser_profile['webdriver']
                logger.info(f"使用HubStudio自带的webdriver: {webdriver_path}")
            
            # 创建Service
            if webdriver_path and os.path.exists(webdriver_path):
                service = Service(webdriver_path)
            else:
                logger.warning("HubStudio webdriver不可用，使用系统ChromeDriver")
                service = Service(ChromeDriverManager().install())
            
            # 创建WebDriver实例
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 测试连接
            driver.get("about:blank")
            logger.info("浏览器连接测试成功")
            
            return driver
            
        except Exception as e:
            logger.error(f"连接浏览器失败: {e}")
            return None
    
    def stop_browser_profile(self):
        """停止浏览器环境"""
        try:
            if self.driver:
                logger.info("关闭WebDriver连接")
                self.driver.quit()
                self.driver = None
            
            if self.profile_id:
                logger.info(f"停止浏览器环境: {self.profile_id}")
                
                # 调用HubStudio API停止浏览器
                stop_url = f"{self.api_url}/api/v1/browser/stop"
                stop_data = {"containerCode": self.profile_id}
                response = requests.post(stop_url, json=stop_data, timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == 0:
                        logger.info("浏览器环境已停止")
                        return True
                    else:
                        logger.warning(f"停止浏览器失败: {result.get('msg')}")
                        return False
                else:
                    logger.warning(f"停止浏览器API请求失败: {response.status_code}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"停止浏览器环境异常: {e}")
            return False
    
    def get_browser_info(self):
        """获取当前浏览器信息"""
        if self.browser_profile:
            return {
                'profile_id': self.profile_id,
                'debug_port': self.browser_profile.get('debug_port'),
                'status': 'running' if self.driver else 'stopped'
            }
        return None
    
    def check_hubstudio_connection(self):
        """检查HubStudio连接状态"""
        try:
            response = requests.post(f"{self.api_url}/api/v1/env/list",
                                   json={"current": 1, "size": 1}, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            if self.driver:
                self.driver.quit()
        except:
            pass
