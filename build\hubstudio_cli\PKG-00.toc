('C:\\Users\\<USER>\\Desktop\\project\\dianshang\\build\\hubstudio_cli\\HubStudio_YouTube_Uploader_CLI_Fixed.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\build\\hubstudio_cli\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\build\\hubstudio_cli\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\build\\hubstudio_cli\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\build\\hubstudio_cli\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\build\\hubstudio_cli\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\build\\hubstudio_cli\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\main.py',
   'PYSOURCE'),
  ('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_avif.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\yaml\\_yaml.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python3.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pywin32_system32\\pywintypes313.dll',
   'BINARY'),
  ('config.ini',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\config.ini',
   'DATA'),
  ('logs\\automation.log',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\logs\\automation.log',
   'DATA'),
  ('screenshots\\after_kids_content_1753830928.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\after_kids_content_1753830928.png',
   'DATA'),
  ('screenshots\\after_kids_content_1753831333.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\after_kids_content_1753831333.png',
   'DATA'),
  ('screenshots\\after_kids_content_1753864640.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\after_kids_content_1753864640.png',
   'DATA'),
  ('screenshots\\after_kids_content_1753866051.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\after_kids_content_1753866051.png',
   'DATA'),
  ('screenshots\\after_kids_content_1753866499.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\after_kids_content_1753866499.png',
   'DATA'),
  ('screenshots\\after_publish_1753831011.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\after_publish_1753831011.png',
   'DATA'),
  ('screenshots\\after_publish_1753831415.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\after_publish_1753831415.png',
   'DATA'),
  ('screenshots\\after_publish_1753864723.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\after_publish_1753864723.png',
   'DATA'),
  ('screenshots\\after_publish_1753866134.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\after_publish_1753866134.png',
   'DATA'),
  ('screenshots\\after_publish_1753866580.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\after_publish_1753866580.png',
   'DATA'),
  ('screenshots\\before_kids_content_1753830927.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\before_kids_content_1753830927.png',
   'DATA'),
  ('screenshots\\before_kids_content_1753831332.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\before_kids_content_1753831332.png',
   'DATA'),
  ('screenshots\\before_kids_content_1753864639.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\before_kids_content_1753864639.png',
   'DATA'),
  ('screenshots\\before_kids_content_1753866050.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\before_kids_content_1753866050.png',
   'DATA'),
  ('screenshots\\before_kids_content_1753866498.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\before_kids_content_1753866498.png',
   'DATA'),
  ('screenshots\\checks_page_1753830977.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\checks_page_1753830977.png',
   'DATA'),
  ('screenshots\\checks_page_1753831381.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\checks_page_1753831381.png',
   'DATA'),
  ('screenshots\\checks_page_1753864688.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\checks_page_1753864688.png',
   'DATA'),
  ('screenshots\\checks_page_1753866100.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\checks_page_1753866100.png',
   'DATA'),
  ('screenshots\\checks_page_1753866547.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\checks_page_1753866547.png',
   'DATA'),
  ('screenshots\\details_filled_1753830954.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\details_filled_1753830954.png',
   'DATA'),
  ('screenshots\\details_filled_1753831358.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\details_filled_1753831358.png',
   'DATA'),
  ('screenshots\\details_filled_1753864666.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\details_filled_1753864666.png',
   'DATA'),
  ('screenshots\\details_filled_1753866077.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\details_filled_1753866077.png',
   'DATA'),
  ('screenshots\\details_filled_1753866524.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\details_filled_1753866524.png',
   'DATA'),
  ('screenshots\\details_page_start_1753830917.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\details_page_start_1753830917.png',
   'DATA'),
  ('screenshots\\details_page_start_1753831323.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\details_page_start_1753831323.png',
   'DATA'),
  ('screenshots\\details_page_start_1753864629.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\details_page_start_1753864629.png',
   'DATA'),
  ('screenshots\\details_page_start_1753866041.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\details_page_start_1753866041.png',
   'DATA'),
  ('screenshots\\details_page_start_1753866489.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\details_page_start_1753866489.png',
   'DATA'),
  ('screenshots\\error_1753829886_1753829886.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753829886_1753829886.png',
   'DATA'),
  ('screenshots\\error_1753830186_1753830186.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753830186_1753830186.png',
   'DATA'),
  ('screenshots\\error_1753830477_1753830477.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753830477_1753830477.png',
   'DATA'),
  ('screenshots\\error_1753830481_1753830481.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753830481_1753830481.png',
   'DATA'),
  ('screenshots\\error_1753830485_1753830485.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753830485_1753830485.png',
   'DATA'),
  ('screenshots\\error_1753830488_1753830488.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753830488_1753830488.png',
   'DATA'),
  ('screenshots\\error_1753830498_1753830498.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753830498_1753830498.png',
   'DATA'),
  ('screenshots\\error_1753830902_1753830902.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753830902_1753830902.png',
   'DATA'),
  ('screenshots\\error_1753830916_1753830916.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753830916_1753830916.png',
   'DATA'),
  ('screenshots\\error_1753830920_1753830920.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753830920_1753830920.png',
   'DATA'),
  ('screenshots\\error_1753830923_1753830923.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753830923_1753830923.png',
   'DATA'),
  ('screenshots\\error_1753831296_1753831296.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753831296_1753831296.png',
   'DATA'),
  ('screenshots\\error_1753831310_1753831310.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753831310_1753831310.png',
   'DATA'),
  ('screenshots\\error_1753831314_1753831314.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753831314_1753831314.png',
   'DATA'),
  ('screenshots\\error_1753864595_1753864595.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753864595_1753864595.png',
   'DATA'),
  ('screenshots\\error_1753864599_1753864599.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753864599_1753864599.png',
   'DATA'),
  ('screenshots\\error_1753864603_1753864603.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753864603_1753864603.png',
   'DATA'),
  ('screenshots\\error_1753864614_1753864614.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753864614_1753864614.png',
   'DATA'),
  ('screenshots\\error_1753866025_1753866025.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753866025_1753866025.png',
   'DATA'),
  ('screenshots\\error_1753866029_1753866029.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753866029_1753866029.png',
   'DATA'),
  ('screenshots\\error_1753866034_1753866034.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753866034_1753866034.png',
   'DATA'),
  ('screenshots\\error_1753866042_1753866042.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753866042_1753866042.png',
   'DATA'),
  ('screenshots\\error_1753866462_1753866462.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753866462_1753866462.png',
   'DATA'),
  ('screenshots\\error_1753866623_1753866623.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\error_1753866623_1753866623.png',
   'DATA'),
  ('screenshots\\navigate_to_studio_1753829607.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\navigate_to_studio_1753829607.png',
   'DATA'),
  ('screenshots\\navigate_to_studio_1753830891.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\navigate_to_studio_1753830891.png',
   'DATA'),
  ('screenshots\\navigate_to_studio_1753831303.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\navigate_to_studio_1753831303.png',
   'DATA'),
  ('screenshots\\navigate_to_studio_1753864608.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\navigate_to_studio_1753864608.png',
   'DATA'),
  ('screenshots\\navigate_to_studio_1753866019.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\navigate_to_studio_1753866019.png',
   'DATA'),
  ('screenshots\\navigate_to_studio_1753866468.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\navigate_to_studio_1753866468.png',
   'DATA'),
  ('screenshots\\publish_page_1753830988.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\publish_page_1753830988.png',
   'DATA'),
  ('screenshots\\publish_page_1753831392.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\publish_page_1753831392.png',
   'DATA'),
  ('screenshots\\publish_page_1753864700.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\publish_page_1753864700.png',
   'DATA'),
  ('screenshots\\publish_page_1753866111.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\publish_page_1753866111.png',
   'DATA'),
  ('screenshots\\publish_page_1753866558.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\publish_page_1753866558.png',
   'DATA'),
  ('screenshots\\video_elements_page_1753830965.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\video_elements_page_1753830965.png',
   'DATA'),
  ('screenshots\\video_elements_page_1753831369.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\video_elements_page_1753831369.png',
   'DATA'),
  ('screenshots\\video_elements_page_1753864677.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\video_elements_page_1753864677.png',
   'DATA'),
  ('screenshots\\video_elements_page_1753866088.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\video_elements_page_1753866088.png',
   'DATA'),
  ('screenshots\\video_elements_page_1753866535.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\video_elements_page_1753866535.png',
   'DATA'),
  ('screenshots\\visibility_set_public_1753830992.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\visibility_set_public_1753830992.png',
   'DATA'),
  ('screenshots\\visibility_set_public_1753831396.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\visibility_set_public_1753831396.png',
   'DATA'),
  ('screenshots\\visibility_set_public_1753864704.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\visibility_set_public_1753864704.png',
   'DATA'),
  ('screenshots\\visibility_set_public_1753866115.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\visibility_set_public_1753866115.png',
   'DATA'),
  ('screenshots\\visibility_set_public_1753866561.png',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\screenshots\\visibility_set_public_1753866561.png',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config-3.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('cv2\\config.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('selenium\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v138\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v138\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('cv2\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\project\\dianshang\\build\\hubstudio_cli\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
