"""
HubStudio + YouTube 自动化视频上传主程序
专门用于HubStudio指纹浏览器自动上传视频到YouTube
"""

import os
import sys
import time
import configparser
from pathlib import Path
from loguru import logger
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
# 导入自动化模块
from browser_manager import HubStudioManager
from video_uploader_unified import VideoUploaderUnified
# from video_processor import VideoProcessor  # 极简版不需要


class HubStudioYouTubeAutomation:
    def __init__(self, config_path="config.ini"):
        self.config = configparser.ConfigParser()

        # 处理PyInstaller打包后的路径问题
        self.config_path = self._resolve_config_path(config_path)

        # 读取配置文件
        if os.path.exists(self.config_path):
            try:
                self.config.read(self.config_path, encoding='utf-8')
                logger.info(f"✅ 配置文件加载成功: {self.config_path}")
            except Exception as e:
                logger.error(f"❌ 配置文件读取失败: {e}")
                # 创建默认配置
                self._create_default_config()
        else:
            logger.warning(f"⚠️ 配置文件不存在: {self.config_path}")
            logger.info("📝 创建默认配置文件...")
            self._create_default_config()

        self.setup_logging()
        self.hubstudio_manager = None
        self.video_uploader = None

    def _resolve_config_path(self, config_path):
        """解析配置文件路径，处理PyInstaller打包后的路径问题"""
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe文件
            # 首先尝试exe文件同目录下的配置文件
            exe_dir = os.path.dirname(sys.executable)
            exe_config_path = os.path.join(exe_dir, config_path)

            if os.path.exists(exe_config_path):
                logger.debug(f"使用exe目录下的配置文件: {exe_config_path}")
                return exe_config_path

            # 如果exe目录下没有，尝试临时目录中的配置文件
            temp_config_path = os.path.join(sys._MEIPASS, config_path)
            if os.path.exists(temp_config_path):
                logger.debug(f"使用临时目录中的配置文件: {temp_config_path}")
                return temp_config_path

            # 如果都没有，返回exe目录路径（用于创建新配置文件）
            logger.debug(f"配置文件不存在，将在exe目录创建: {exe_config_path}")
            return exe_config_path
        else:
            # 如果是开发环境，使用相对路径
            abs_config_path = os.path.abspath(config_path)
            logger.debug(f"开发环境配置文件路径: {abs_config_path}")
            return abs_config_path

    def _create_default_config(self):
        """创建默认配置文件"""
        try:
            # 设置默认配置
            self.config['BROWSER'] = {
                'api_url': 'http://127.0.0.1:6873',
                'readonly_mode': 'false',
                'headless_mode': 'false',
                'cdp_hide': 'true',
                'profile_id': ''
            }

            self.config['YOUTUBE'] = {
                'upload_url': 'https://studio.youtube.com'
            }

            self.config['VIDEO'] = {
                'video_folder': './videos',
                'default_title': '',
                'default_description': '',
                'default_tags': '自动化,HubStudio,YouTube,视频上传'
            }

            self.config['AUTOMATION'] = {
                'wait_timeout': '30',
                'upload_timeout': '600',
                'retry_count': '3',
                'upload_interval': '10',
                'batch_interval': '10',
                'screenshot_on_error': 'true'
            }

            self.config['HUBSTUDIO'] = {
                'api_id': '',
                'api_secret': ''
            }

            self.config['LOG'] = {
                'log_level': 'INFO',
                'log_file': './logs/automation.log'
            }

            # 保存配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                self.config.write(f)

            logger.info(f"✅ 默认配置文件已创建: {self.config_path}")
            logger.warning("⚠️ 请编辑配置文件，设置正确的API认证信息")

        except Exception as e:
            logger.error(f"❌ 创建默认配置文件失败: {e}")
            # 如果无法创建文件，至少在内存中设置默认值
            pass
        
    def setup_logging(self):
        """设置日志"""
        try:
            log_level = self.config.get('LOG', 'log_level', fallback='INFO')
            log_file = self.config.get('LOG', 'log_file', fallback='./logs/automation.log')

            # 处理日志文件路径（相对于exe文件位置）
            if getattr(sys, 'frozen', False):
                # 打包后的exe环境
                exe_dir = os.path.dirname(sys.executable)
                if not os.path.isabs(log_file):
                    log_file = os.path.join(exe_dir, log_file)
            else:
                # 开发环境
                log_file = os.path.abspath(log_file)

            # 创建日志目录
            log_dir = os.path.dirname(log_file)
            os.makedirs(log_dir, exist_ok=True)

            # 配置loguru
            logger.remove()

            # 控制台输出
            logger.add(
                sys.stderr,
                level=log_level,
                format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
            )

            # 文件输出
            logger.add(
                log_file,
                rotation="10 MB",
                retention="7 days",
                level=log_level,
                encoding="utf-8",
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
            )

            logger.info(f"📝 日志系统初始化成功")
            logger.info(f"   日志级别: {log_level}")
            logger.info(f"   日志文件: {log_file}")

        except Exception as e:
            # 如果日志设置失败，至少保证控制台输出
            print(f"警告：日志设置失败: {e}")
            logger.remove()
            logger.add(sys.stderr, level="INFO")
        
    def initialize(self, profile_id=None):
        """初始化HubStudio浏览器和YouTube上传器"""
        try:
            logger.info("🚀 开始初始化HubStudio + YouTube自动化系统")

            # 初始化HubStudio管理器
            logger.info("📋 初始化HubStudio管理器...")
            self.hubstudio_manager = HubStudioManager(self.config, profile_id)

            # 检查HubStudio连接
            if not self.hubstudio_manager.check_hubstudio_connection():
                logger.error("❌ HubStudio连接检查失败")
                logger.error("💡 请确保：")
                logger.error("   1. HubStudio客户端正在运行")
                logger.error("   2. API地址配置正确 (默认: http://127.0.0.1:6873)")
                logger.error("   3. 网络连接正常")
                return False

            # 启动HubStudio浏览器
            logger.info("🌐 启动HubStudio浏览器环境...")
            driver = self.hubstudio_manager.start_browser_profile()
            if not driver:
                logger.error("❌ HubStudio浏览器启动失败")
                logger.error("💡 可能的原因：")
                logger.error("   1. 浏览器环境不存在或已损坏")
                logger.error("   2. API认证信息错误")
                logger.error("   3. 浏览器环境正在被其他程序使用")
                logger.error("   4. 系统资源不足")

                # 清理资源
                self.cleanup()
                return False

            # 验证浏览器连接
            try:
                logger.info("🧪 验证浏览器连接...")
                test_url = driver.current_url
                driver_title = driver.title
                logger.info(f"✅ 浏览器连接验证成功")
                logger.debug(f"   当前URL: {test_url}")
                logger.debug(f"   页面标题: {driver_title}")
            except Exception as e:
                logger.error(f"❌ 浏览器连接验证失败: {e}")
                self.cleanup()
                return False

            # 初始化统一视频上传器
            logger.info("📤 初始化统一视频上传器...")
            try:
                self.video_uploader = VideoUploaderUnified(driver, self.config)
                logger.info("✅ 视频上传器初始化成功")
            except Exception as e:
                logger.error(f"❌ 视频上传器初始化失败: {e}")
                self.cleanup()
                return False

            logger.info("🎉 HubStudio + YouTube 自动化系统初始化成功")
            logger.info("📊 系统状态:")
            logger.info(f"   - HubStudio管理器: ✅ 已连接")
            logger.info(f"   - 浏览器环境: ✅ 已启动")
            logger.info(f"   - 视频上传器: ✅ 已就绪")

            return True

        except Exception as e:
            logger.error(f"❌ 初始化过程中发生异常: {e}")
            import traceback
            logger.debug(f"详细错误: {traceback.format_exc()}")

            # 确保清理资源
            self.cleanup()
            return False
    
    def upload_single_video(self, video_path, title=None, description=None, tags=None, children_content=None):
        """上传单个视频"""
        try:
            # 检查系统状态
            if not self.hubstudio_manager or not self.video_uploader:
                logger.error("❌ 系统未正确初始化，无法上传视频")
                return False

            # 检查浏览器连接状态
            if not self.hubstudio_manager.driver:
                logger.error("❌ 浏览器连接已断开，无法上传视频")
                return False

            # 验证浏览器是否仍然可用
            try:
                current_url = self.hubstudio_manager.driver.current_url
                logger.debug(f"浏览器状态检查通过，当前URL: {current_url}")
            except Exception as e:
                logger.error(f"❌ 浏览器连接已失效: {e}")
                return False

            # 检查视频文件
            if not os.path.exists(video_path):
                logger.error(f"❌ 视频文件不存在: {video_path}")
                return False

            # 获取文件信息
            file_size = os.path.getsize(video_path)
            file_size_mb = file_size / (1024 * 1024)
            logger.info(f"📁 视频文件: {os.path.basename(video_path)} ({file_size_mb:.1f} MB)")

            # 设置默认值
            if not title:
                title = os.path.splitext(os.path.basename(video_path))[0]  # 去掉扩展名
            if not description:
                description = f"视频文件: {os.path.basename(video_path)}"
            if not tags:
                tags = self.config.get('VIDEO', 'default_tags', fallback='automation,video,upload')

            logger.info(f"🚀 开始上传视频: {video_path}")
            logger.info(f"📝 标题: {title}")
            logger.info(f"📄 描述: {description[:100]}{'...' if len(description) > 100 else ''}")
            logger.info(f"🏷️ 标签: {tags}")
            logger.info(f"👶 儿童内容: {'是' if children_content else '否'}")

            # 执行统一视频上传
            logger.info("🎬 开始执行上传流程...")
            success = self.video_uploader.upload_video(video_path, title, description, children_content, tags)

            if success:
                logger.info(f"🎉 YouTube视频上传成功: {os.path.basename(video_path)}")
                logger.info("✅ 上传完成，视频正在处理中")
            else:
                logger.error(f"❌ YouTube视频上传失败: {os.path.basename(video_path)}")

                # 截图保存错误状态
                if self.config.getboolean('AUTOMATION', 'screenshot_on_error', fallback=True):
                    try:
                        timestamp = int(time.time())
                        self.video_uploader._take_screenshot(f"error_{timestamp}")
                        logger.info(f"📸 错误截图已保存: error_{timestamp}.png")
                    except Exception as screenshot_e:
                        logger.warning(f"⚠️ 保存错误截图失败: {screenshot_e}")

            return success

        except Exception as e:
            logger.error(f"❌ 上传视频过程中发生异常: {e}")
            import traceback
            logger.debug(f"详细错误: {traceback.format_exc()}")
            return False
    
    def upload_batch_videos(self, video_folder=None):
        """批量上传视频"""
        try:
            if not video_folder:
                video_folder = self.config.get('VIDEO', 'video_folder', fallback='./videos')

            if not os.path.exists(video_folder):
                logger.error(f"视频文件夹不存在: {video_folder}")
                return False

            # 极简版：直接扫描文件夹
            video_files = self._scan_video_folder_simple(video_folder)

            if not video_files:
                logger.warning(f"在文件夹中未找到视频文件: {video_folder}")
                return False

            logger.info(f"找到 {len(video_files)} 个视频文件")
            
            # 批量上传
            success_count = 0
            retry_count = int(self.config.get('AUTOMATION', 'retry_count', fallback=3))
            upload_interval = int(self.config.get('AUTOMATION', 'upload_interval', fallback=10))

            for i, video_file in enumerate(video_files):
                video_path = str(video_file)
                video_name = Path(video_file).stem

                logger.info(f"处理视频 {i+1}/{len(video_files)}: {video_name}")
                
                # 尝试上传
                for attempt in range(retry_count):
                    try:
                        logger.info(f"上传视频 ({attempt + 1}/{retry_count}): {video_name}")
                        
                        if self.upload_single_video(video_path, title=video_name):
                            success_count += 1
                            break
                        else:
                            if attempt < retry_count - 1:
                                logger.warning(f"上传失败，等待重试...")
                                time.sleep(10)
                            else:
                                logger.error(f"视频上传最终失败: {video_name}")
                                
                    except Exception as e:
                        logger.error(f"上传视频异常: {e}")
                        if attempt < retry_count - 1:
                            time.sleep(10)
                
                # 上传间隔
                if i < len(video_files) - 1:  # 不是最后一个文件
                    logger.info(f"等待 {upload_interval} 秒后处理下一个视频...")
                    time.sleep(upload_interval)
                
            logger.info(f"批量上传完成: 成功 {success_count}/{len(video_files)}")
            return success_count > 0

        except Exception as e:
            logger.error(f"批量上传异常: {e}")
            return False

    def _scan_video_folder_simple(self, folder_path):
        """极简版文件夹扫描"""
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm']
        video_files = []

        for file in os.listdir(folder_path):
            if any(file.lower().endswith(ext) for ext in video_extensions):
                video_files.append(os.path.join(folder_path, file))

        return video_files

    def cleanup(self):
        """清理资源"""
        try:
            logger.info("清理资源...")
            
            if self.video_uploader:
                self.video_uploader.close()
                
            if self.hubstudio_manager:
                self.hubstudio_manager.stop_browser_profile()
                
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源失败: {e}")


def main():
    """主函数"""
    automation = HubStudioYouTubeAutomation()
    
    try:
        # 初始化
        if not automation.initialize():
            logger.error("初始化失败，程序退出")
            return
            
        # 检查命令行参数
        if len(sys.argv) > 1:
            video_path = sys.argv[1]
            if os.path.isfile(video_path):
                # 上传单个视频
                automation.upload_single_video(video_path)
            elif os.path.isdir(video_path):
                # 批量上传
                automation.upload_batch_videos(video_path)
            else:
                logger.error(f"无效的路径: {video_path}")
        else:
            # 使用配置文件中的默认文件夹
            automation.upload_batch_videos()
            
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序异常: {e}")
    finally:
        automation.cleanup()


if __name__ == "__main__":
    main()
