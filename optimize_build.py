#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化打包脚本 - 减小exe文件大小
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path

def optimize_spec_files():
    """优化spec文件以减小打包大小"""
    
    # 优化GUI版本spec文件
    gui_spec_content = '''# -*- mode: python ; coding: utf-8 -*-
"""
HubStudio + YouTube 自动化上传工具 - GUI版本（优化版）
"""

import os
import sys

project_root = os.path.dirname(os.path.abspath(SPEC))

# 最小化数据文件
datas = [
    (os.path.join(project_root, 'config.ini'), '.'),
    (os.path.join(project_root, 'gui'), 'gui'),
]

# 精简隐藏导入
hiddenimports = [
    'tkinter', 'tkinter.ttk', 'tkinter.filedialog', 'tkinter.messagebox',
    'selenium', 'selenium.webdriver', 'selenium.webdriver.chrome',
    'webdriver_manager', 'webdriver_manager.chrome',
    'loguru', 'requests', 'configparser',
    'browser_manager', 'video_uploader_unified',
    'gui.gui_controller', 'gui.gui_view_simple', 'main',
]

# 排除更多不需要的模块
excludes = [
    'matplotlib', 'scipy', 'pandas', 'jupyter', 'notebook', 'IPython',
    'PIL.ImageQt', 'PIL.ImageTk', 'PIL.ImageWin',
    'cv2.gapi', 'cv2.dnn', 'cv2.ml', 'cv2.objdetect',
    'numpy.distutils', 'numpy.f2py', 'numpy.testing',
    'selenium.webdriver.firefox', 'selenium.webdriver.safari',
    'selenium.webdriver.edge', 'selenium.webdriver.ie',
]

a = Analysis(
    ['start_gui.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=2,  # 最高优化级别
)

# 移除重复文件
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='HubStudio_YouTube_Uploader_GUI_Optimized',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # 启用strip
    upx=True,    # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    # 优化CLI版本spec文件
    cli_spec_content = '''# -*- mode: python ; coding: utf-8 -*-
"""
HubStudio + YouTube 自动化上传工具 - 命令行版本（优化版）
"""

import os
import sys

project_root = os.path.dirname(os.path.abspath(SPEC))

# 最小化数据文件
datas = [
    (os.path.join(project_root, 'config.ini'), '.'),
]

# 精简隐藏导入
hiddenimports = [
    'selenium', 'selenium.webdriver', 'selenium.webdriver.chrome',
    'webdriver_manager', 'webdriver_manager.chrome',
    'loguru', 'requests', 'configparser',
    'browser_manager', 'video_uploader_unified',
]

# 排除更多不需要的模块
excludes = [
    'matplotlib', 'scipy', 'pandas', 'jupyter', 'notebook', 'IPython',
    'tkinter', 'PIL.ImageQt', 'PIL.ImageTk', 'PIL.ImageWin',
    'cv2.gapi', 'cv2.dnn', 'cv2.ml', 'cv2.objdetect',
    'numpy.distutils', 'numpy.f2py', 'numpy.testing',
    'selenium.webdriver.firefox', 'selenium.webdriver.safari',
    'selenium.webdriver.edge', 'selenium.webdriver.ie',
]

a = Analysis(
    ['main.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=2,  # 最高优化级别
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='HubStudio_YouTube_Uploader_CLI_Optimized',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # 启用strip
    upx=True,    # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    # 写入优化后的spec文件
    with open('hubstudio_gui_optimized.spec', 'w', encoding='utf-8') as f:
        f.write(gui_spec_content)
    
    with open('hubstudio_cli_optimized.spec', 'w', encoding='utf-8') as f:
        f.write(cli_spec_content)
    
    print("✅ 优化版spec文件已创建")

def clean_build():
    """清理构建文件"""
    dirs_to_clean = ['build', 'dist', '__pycache__', 'gui/__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🗑️ 已清理: {dir_name}")

def build_optimized():
    """构建优化版本"""
    print("🚀 开始构建优化版本...")
    
    # 构建GUI版本
    print("📱 构建优化GUI版本...")
    result = subprocess.run([
        sys.executable, '-m', 'PyInstaller', 
        '--clean', 'hubstudio_gui_optimized.spec'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ GUI版本构建失败: {result.stderr}")
        return False
    
    # 构建CLI版本
    print("💻 构建优化CLI版本...")
    result = subprocess.run([
        sys.executable, '-m', 'PyInstaller', 
        '--clean', 'hubstudio_cli_optimized.spec'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ CLI版本构建失败: {result.stderr}")
        return False
    
    print("✅ 优化版本构建完成")
    return True

def compare_sizes():
    """比较文件大小"""
    print("\n📊 文件大小对比:")
    
    files = [
        'dist/HubStudio_YouTube_Uploader_GUI.exe',
        'dist/HubStudio_YouTube_Uploader_GUI_Optimized.exe',
        'dist/HubStudio_YouTube_Uploader_CLI.exe',
        'dist/HubStudio_YouTube_Uploader_CLI_Optimized.exe',
    ]
    
    for file_path in files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            size_mb = size / (1024 * 1024)
            print(f"  {os.path.basename(file_path)}: {size_mb:.1f} MB")
        else:
            print(f"  {os.path.basename(file_path)}: 文件不存在")

def main():
    """主函数"""
    print("🔧 HubStudio YouTube上传工具 - 打包优化脚本")
    print("=" * 50)
    
    # 1. 创建优化版spec文件
    optimize_spec_files()
    
    # 2. 清理旧文件
    clean_build()
    
    # 3. 构建优化版本
    if build_optimized():
        # 4. 比较文件大小
        compare_sizes()
        
        print("\n🎉 优化完成！")
        print("💡 建议使用优化版本以获得更小的文件大小")
    else:
        print("\n❌ 优化失败，请检查错误信息")

if __name__ == "__main__":
    main()
