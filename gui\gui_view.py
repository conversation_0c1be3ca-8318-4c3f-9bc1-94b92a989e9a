"""
GUI视图 - 负责界面展示和用户交互
简化版本，去掉自定义样式
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os


class GUIView:
    """GUI视图类 - 负责界面展示"""
    
    def __init__(self, root, controller):
        self.root = root
        self.controller = controller
        
        # 设置窗口基本属性
        self.root.title("HubStudio + YouTube 自动化上传工具")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        self.root.resizable(True, True)
        
        # 初始化界面变量
        self.init_variables()
        
        # 创建界面
        self.create_widgets()
        
        # 设置控制器回调
        self.setup_controller_callbacks()
        
        # 启动日志更新
        self.update_logs()
    
    def init_variables(self):
        """初始化界面变量"""
        # 视频相关变量
        self.video_count_var = tk.StringVar(value="未选择视频")
        self.video_path_var = tk.StringVar(value="")
        
        # 配置相关变量
        self.api_url_var = tk.StringVar(value=self.controller.get_config('BROWSER', 'api_url'))
        self.api_id_var = tk.StringVar(value=self.controller.get_config('HUBSTUDIO', 'api_id'))
        self.api_secret_var = tk.StringVar(value=self.controller.get_config('HUBSTUDIO', 'api_secret'))
        
        # 视频信息变量
        self.title_var = tk.StringVar(value=self.controller.get_config('VIDEO', 'default_title'))
        self.children_content_var = tk.StringVar(value="否")
        
        # 并发设置变量
        self.concurrent_count_var = tk.IntVar(value=1)
        
        # 高级设置变量
        self.upload_interval_var = tk.StringVar(value=self.controller.get_config('AUTOMATION', 'upload_interval'))
        self.timeout_var = tk.StringVar(value=self.controller.get_config('AUTOMATION', 'wait_timeout'))
        
        # 状态相关变量
        self.hubstudio_status_var = tk.StringVar(value="正在检测HubStudio环境...")
        self.api_status_var = tk.StringVar(value="未连接")
        self.env_count_var = tk.StringVar(value="0 个环境")
        self.status_var = tk.StringVar(value="就绪")
        self.progress_var = tk.DoubleVar()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=0)  # 标题栏
        main_frame.rowconfigure(1, weight=0)  # 视频选择栏
        main_frame.rowconfigure(2, weight=1)  # 主要内容区域
        main_frame.rowconfigure(3, weight=0)  # 日志区域
        main_frame.rowconfigure(4, weight=0)  # 状态栏
        
        # 创建各个区域
        self.create_title_bar(main_frame)
        self.create_video_selection_bar(main_frame)
        self.create_content_area(main_frame)
        self.create_log_panel(main_frame)
        self.create_status_bar(main_frame)
    
    def create_title_bar(self, parent):
        """创建标题栏"""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        title_frame.columnconfigure(0, weight=1)

        # 主标题
        title_label = ttk.Label(title_frame, text="HubStudio + YouTube 自动化上传", 
                               font=('Arial', 18, 'bold'))
        title_label.grid(row=0, column=0, sticky=tk.W)

        # 副标题
        subtitle_label = ttk.Label(title_frame, text="高效、智能的视频批量上传解决方案", 
                                  font=('Arial', 12))
        subtitle_label.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))

        # 分隔线
        separator = ttk.Separator(title_frame, orient='horizontal')
        separator.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
    
    def create_video_selection_bar(self, parent):
        """创建视频选择栏"""
        video_frame = ttk.LabelFrame(parent, text="视频文件", padding="15")
        video_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        video_frame.columnconfigure(2, weight=1)

        # 选择视频按钮
        select_btn = ttk.Button(video_frame, text="选择视频文件", 
                               command=self.on_select_video)
        select_btn.grid(row=0, column=0, padx=(0, 10))

        # 视频状态显示
        status_label = ttk.Label(video_frame, textvariable=self.video_count_var)
        status_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))

        # 视频文件路径显示框
        self.video_path_entry = ttk.Entry(video_frame, textvariable=self.video_path_var, 
                                         state='readonly', width=50)
        self.video_path_entry.grid(row=0, column=2, sticky=(tk.W, tk.E), padx=(0, 10))

        # 操作按钮组
        button_frame = ttk.Frame(video_frame)
        button_frame.grid(row=0, column=3)

        # 清除视频按钮
        clear_btn = ttk.Button(button_frame, text="清除", command=self.on_clear_video)
        clear_btn.grid(row=0, column=0, padx=(0, 8))

        # 开始上传按钮
        self.upload_btn = ttk.Button(button_frame, text="开始上传", 
                                    command=self.on_start_upload, state='disabled')
        self.upload_btn.grid(row=0, column=1, padx=(0, 8))

        # 停止上传按钮
        self.stop_btn = ttk.Button(button_frame, text="停止上传", 
                                  command=self.on_stop_upload, state='disabled')
        self.stop_btn.grid(row=0, column=2)
    
    def create_content_area(self, parent):
        """创建主要内容区域"""
        content_frame = ttk.Frame(parent)
        content_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=15)
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # 左侧面板 - 配置和控制
        self.create_control_panel(content_frame)
        
        # 右侧面板 - 设置
        self.create_settings_panel(content_frame)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_main_frame = ttk.LabelFrame(parent, text="视频配置", padding="10")
        control_main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        control_main_frame.columnconfigure(0, weight=1)
        control_main_frame.rowconfigure(0, weight=1)

        # 创建滚动区域
        control_canvas_frame = ttk.Frame(control_main_frame)
        control_canvas_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        control_canvas_frame.columnconfigure(0, weight=1)
        control_canvas_frame.rowconfigure(0, weight=1)

        self.control_canvas = tk.Canvas(control_canvas_frame, highlightthickness=0, borderwidth=0)
        self.control_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        control_scrollbar = ttk.Scrollbar(control_canvas_frame, orient="vertical", command=self.control_canvas.yview)
        control_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.control_canvas.configure(yscrollcommand=control_scrollbar.set)
        
        self.control_scrollable_frame = ttk.Frame(self.control_canvas)
        self.control_canvas_window = self.control_canvas.create_window((0, 0), 
                                                                      window=self.control_scrollable_frame, 
                                                                      anchor="nw")
        
        # 绑定事件
        self.control_scrollable_frame.bind("<Configure>", self._on_control_frame_configure)
        self.control_canvas.bind("<Configure>", self._on_control_canvas_configure)
        self.control_canvas.bind("<MouseWheel>", self._on_control_mousewheel)
        
        # 创建控制面板内容
        self._create_control_content(self.control_scrollable_frame)
        
        # 递归绑定所有子组件的滚轮事件
        self._bind_control_mousewheel_recursively(control_main_frame)
    
    def _create_control_content(self, parent):
        """创建控制面板内容"""
        parent.columnconfigure(0, weight=1)
        
        # HubStudio状态显示区域
        status_frame = ttk.LabelFrame(parent, text="连接状态", padding="10")
        status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        status_frame.columnconfigure(0, weight=1)

        self.status_display = ttk.Label(status_frame, textvariable=self.hubstudio_status_var)
        self.status_display.grid(row=0, column=0, sticky=tk.W)

        # 并发设置框架
        concurrent_frame = ttk.LabelFrame(parent, text="并发设置", padding="10")
        concurrent_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        concurrent_frame.columnconfigure(1, weight=1)

        # 并发数量设置
        spinbox_frame = ttk.Frame(concurrent_frame)
        spinbox_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        ttk.Label(spinbox_frame, text="并发数量", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
        concurrent_spinbox = ttk.Spinbox(spinbox_frame, from_=1, to=10, textvariable=self.concurrent_count_var, 
                                        width=8)
        concurrent_spinbox.grid(row=0, column=1, padx=(0, 8))
        ttk.Label(spinbox_frame, text="个环境").grid(row=0, column=2, sticky=tk.W)
        
        # 并发说明
        help_frame = ttk.Frame(concurrent_frame)
        help_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        help_frame.columnconfigure(0, weight=1)
        
        help_text = ttk.Label(help_frame, 
                             text="说明：分批处理所有环境。例如10个环境，并发数2，则每批处理2个环境，直到所有环境完成",
                             font=('Arial', 9), foreground='gray')
        help_text.grid(row=0, column=0, sticky=tk.W)
        help_text.config(wraplength=350)

        # 视频信息框架
        video_info_frame = ttk.LabelFrame(parent, text="视频信息", padding="10")
        video_info_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        video_info_frame.columnconfigure(0, weight=1)
        video_info_frame.rowconfigure(3, weight=1)

        # 视频标题
        ttk.Label(video_info_frame, text="视频标题:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        title_entry = ttk.Entry(video_info_frame, textvariable=self.title_var)
        title_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 视频描述
        ttk.Label(video_info_frame, text="视频描述:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        desc_frame = ttk.Frame(video_info_frame)
        desc_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        desc_frame.columnconfigure(0, weight=1)
        desc_frame.rowconfigure(0, weight=1)

        self.desc_text = tk.Text(desc_frame, height=4, wrap=tk.WORD, font=('Arial', 10))
        self.desc_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 设置默认描述
        default_desc = self.controller.get_config('VIDEO', 'default_description')
        self.desc_text.insert('1.0', default_desc)

        desc_scrollbar = ttk.Scrollbar(desc_frame, orient='vertical', command=self.desc_text.yview)
        desc_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.desc_text.configure(yscrollcommand=desc_scrollbar.set)

        # 儿童内容设置 - 内容分级为是否为儿童专属内容
        ttk.Label(video_info_frame, text="内容分级 (是否为儿童专属内容):").grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        children_frame = ttk.Frame(video_info_frame)
        children_frame.grid(row=5, column=0, sticky=(tk.W, tk.E))

        ttk.Radiobutton(children_frame, text="是，这是为儿童制作的内容", variable=self.children_content_var, 
                       value="是").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(children_frame, text="否，这不是为儿童制作的内容", variable=self.children_content_var, 
                       value="否").pack(side=tk.LEFT)
    
    def create_settings_panel(self, parent):
        """创建设置面板"""
        settings_main_frame = ttk.LabelFrame(parent, text="HubStudio 设置", padding="10")
        settings_main_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        settings_main_frame.columnconfigure(0, weight=1)
        settings_main_frame.rowconfigure(0, weight=1)

        # 创建滚动区域
        canvas_frame = ttk.Frame(settings_main_frame)
        canvas_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        canvas_frame.columnconfigure(0, weight=1)
        canvas_frame.rowconfigure(0, weight=1)

        self.settings_canvas = tk.Canvas(canvas_frame, highlightthickness=0, borderwidth=0)
        self.settings_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        settings_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.settings_canvas.yview)
        settings_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.settings_canvas.configure(yscrollcommand=settings_scrollbar.set)
        
        self.settings_scrollable_frame = ttk.Frame(self.settings_canvas)
        self.settings_canvas_window = self.settings_canvas.create_window((0, 0), 
                                                                        window=self.settings_scrollable_frame, 
                                                                        anchor="nw")
        
        # 绑定事件
        self.settings_scrollable_frame.bind("<Configure>", self._on_settings_frame_configure)
        self.settings_canvas.bind("<Configure>", self._on_settings_canvas_configure)
        self.settings_canvas.bind("<MouseWheel>", self._on_settings_mousewheel)
        
        # 绑定鼠标进入事件，确保滚轮焦点
        self.settings_canvas.bind("<Enter>", self._on_settings_canvas_enter)
        self.settings_canvas.bind("<Leave>", self._on_settings_canvas_leave)
        
        # 为整个设置面板绑定滚轮事件
        settings_main_frame.bind("<MouseWheel>", self._on_settings_mousewheel)
        
        # 创建设置内容
        # 创建设置内容
        self._create_settings_content(self.settings_scrollable_frame)
        
        # 递归绑定所有子组件的滚轮事件
        self._bind_mousewheel_recursively(settings_main_frame)
    
    def _create_settings_content(self, parent):
        """创建设置面板内容"""
        parent.columnconfigure(0, weight=1)
        
        # API状态显示区域
        status_frame = ttk.LabelFrame(parent, text="API 连接状态", padding="10")
        status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(0, weight=1)

        self.api_status_label = ttk.Label(status_frame, textvariable=self.api_status_var)
        self.api_status_label.grid(row=0, column=0, sticky=tk.W)
        
        # API配置区域
        config_frame = ttk.LabelFrame(parent, text="API 配置", padding="10")
        config_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(0, weight=1)

        # API地址配置
        ttk.Label(config_frame, text="API地址:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        api_url_entry = ttk.Entry(config_frame, textvariable=self.api_url_var)
        api_url_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 8))

        # API ID配置
        ttk.Label(config_frame, text="API ID:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        api_id_entry = ttk.Entry(config_frame, textvariable=self.api_id_var)
        api_id_entry.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 8))

        # API Secret配置
        ttk.Label(config_frame, text="API Secret:").grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        api_secret_entry = ttk.Entry(config_frame, textvariable=self.api_secret_var, show="*")
        api_secret_entry.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        
        # 配置说明
        info_label = ttk.Label(config_frame, text="请在HubStudio中获取API配置信息", 
                              font=('Arial', 9), foreground='gray')
        info_label.grid(row=6, column=0, sticky=tk.W, pady=(5, 0))
        
        # 环境信息区域
        env_frame = ttk.LabelFrame(parent, text="环境信息", padding="10")
        env_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        env_frame.columnconfigure(0, weight=1)
        
        # 环境数量显示
        env_count_label = ttk.Label(env_frame, textvariable=self.env_count_var)
        env_count_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        # 环境列表显示
        self.env_list_text = tk.Text(env_frame, height=3, wrap=tk.WORD, font=('Consolas', 9),
                                    borderwidth=1, relief='solid', state='disabled')
        self.env_list_text.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 操作按钮区域
        action_frame = ttk.LabelFrame(parent, text="操作中心", padding="10")
        action_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        action_frame.columnconfigure(0, weight=1)
        action_frame.columnconfigure(1, weight=1)

        # 第一行按钮
        save_btn = ttk.Button(action_frame, text="保存配置", command=self.on_save_config)
        save_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5), pady=(0, 8))

        test_btn = ttk.Button(action_frame, text="测试连接", command=self.on_test_connection)
        test_btn.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(0, 8))

        # 第二行按钮
        refresh_btn = ttk.Button(action_frame, text="刷新环境", command=self.on_refresh_environments)
        refresh_btn.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=(0, 5), pady=(0, 8))

        help_btn = ttk.Button(action_frame, text="使用帮助", command=self.on_show_help)
        help_btn.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(0, 8))
        
        # 高级设置区域
        advanced_frame = ttk.LabelFrame(parent, text="高级设置", padding="10")
        advanced_frame.grid(row=4, column=0, sticky=(tk.W, tk.E))
        advanced_frame.columnconfigure(0, weight=1)

        # 上传间隔设置
        interval_frame = ttk.Frame(advanced_frame)
        interval_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        interval_frame.columnconfigure(1, weight=1)

        ttk.Label(interval_frame, text="上传间隔", font=('Arial', 10, 'bold'), width=12).grid(row=0, column=0, sticky=tk.W)
        interval_spinbox = ttk.Spinbox(interval_frame, from_=1, to=60, textvariable=self.upload_interval_var, width=10)
        interval_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(8, 0))
        ttk.Label(interval_frame, text="秒").grid(row=0, column=2, sticky=tk.W, padx=(5, 0))

        # 超时设置
        timeout_frame = ttk.Frame(advanced_frame)
        timeout_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        timeout_frame.columnconfigure(1, weight=1)

        ttk.Label(timeout_frame, text="操作超时", font=('Arial', 10, 'bold'), width=12).grid(row=0, column=0, sticky=tk.W)
        timeout_spinbox = ttk.Spinbox(timeout_frame, from_=10, to=300, textvariable=self.timeout_var, width=10)
        timeout_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(8, 0))
        ttk.Label(timeout_frame, text="秒").grid(row=0, column=2, sticky=tk.W, padx=(5, 0))
    
    def create_log_panel(self, parent):
        """创建日志面板"""
        log_frame = ttk.LabelFrame(parent, text="实时日志", padding="15")
        log_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(15, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(1, weight=1)

        # 日志工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 8))
        log_toolbar.columnconfigure(0, weight=1)

        ttk.Label(log_toolbar, text="运行日志", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W)
        clear_btn = ttk.Button(log_toolbar, text="清除日志", command=self.on_clear_logs)
        clear_btn.grid(row=0, column=1, sticky=tk.E)

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, wrap=tk.WORD, font=('Consolas', 9))
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.LabelFrame(parent, text="状态", padding="15")
        status_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
        status_frame.columnconfigure(1, weight=1)

        # 状态信息区域
        status_info = ttk.Frame(status_frame)
        status_info.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 8))
        status_info.columnconfigure(1, weight=1)

        # 状态标签
        ttk.Label(status_info, text="状态", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W)
        self.status_label = ttk.Label(status_info, textvariable=self.status_var)
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        # 进度条区域
        progress_frame = ttk.Frame(status_frame)
        progress_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        progress_frame.columnconfigure(1, weight=1)

        ttk.Label(progress_frame, text="进度", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W)
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100, length=400)
        self.progress_bar.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
    
    # ==================== 滚动事件处理 ====================
    
    def _on_settings_frame_configure(self, event):
        """当内容框架大小改变时更新滚动区域"""
        self.settings_canvas.configure(scrollregion=self.settings_canvas.bbox("all"))
    
    def _on_settings_canvas_configure(self, event):
        """当Canvas大小改变时调整内容框架宽度"""
        canvas_width = event.width
        self.settings_canvas.itemconfig(self.settings_canvas_window, width=canvas_width)
    
    def _on_settings_mousewheel(self, event):
        """处理设置面板鼠标滚轮事件"""
        # 改进滚轮响应，支持更流畅的滚动
        if event.delta:
            # Windows系统
            self.settings_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        else:
            # Linux系统
            if event.num == 4:
                self.settings_canvas.yview_scroll(-1, "units")
            elif event.num == 5:
                self.settings_canvas.yview_scroll(1, "units")
    
    def _on_control_frame_configure(self, event):
        """当控制面板内容框架大小改变时更新滚动区域"""
        self.control_canvas.configure(scrollregion=self.control_canvas.bbox("all"))
    
    def _on_control_canvas_configure(self, event):
        """当控制面板Canvas大小改变时调整内容框架宽度"""
        canvas_width = event.width
        self.control_canvas.itemconfig(self.control_canvas_window, width=canvas_width)
    
    def _on_control_mousewheel(self, event):
        """处理控制面板鼠标滚轮事件"""
        # 改进滚轮响应，支持更流畅的滚动
        if event.delta:
            # Windows系统
            self.control_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        else:
            # Linux系统
            if event.num == 4:
                self.control_canvas.yview_scroll(-1, "units")
            elif event.num == 5:
                self.control_canvas.yview_scroll(1, "units")
    
    def _on_settings_canvas_enter(self, event):
        """鼠标进入Canvas时设置焦点"""
        self.settings_canvas.focus_set()
    
    def _on_settings_canvas_leave(self, event):
        """鼠标离开Canvas时释放焦点"""
        self.root.focus_set()
    
    def _bind_mousewheel_recursively(self, widget):
        """递归绑定设置面板所有子组件的滚轮事件"""
        try:
            # 为当前组件绑定滚轮事件
            widget.bind("<MouseWheel>", self._on_settings_mousewheel)
            widget.bind("<Button-4>", self._on_settings_mousewheel)  # Linux向上滚动
            widget.bind("<Button-5>", self._on_settings_mousewheel)  # Linux向下滚动
            
            # 递归处理所有子组件
            for child in widget.winfo_children():
                self._bind_mousewheel_recursively(child)
        except Exception:
            # 忽略不支持绑定事件的组件
            pass
    
    def _bind_control_mousewheel_recursively(self, widget):
        """递归绑定控制面板所有子组件的滚轮事件"""
        try:
            # 为当前组件绑定滚轮事件
            widget.bind("<MouseWheel>", self._on_control_mousewheel)
            widget.bind("<Button-4>", self._on_control_mousewheel)  # Linux向上滚动
            widget.bind("<Button-5>", self._on_control_mousewheel)  # Linux向下滚动
            
            # 递归处理所有子组件
            for child in widget.winfo_children():
                self._bind_control_mousewheel_recursively(child)
        except Exception:
            # 忽略不支持绑定事件的组件
            pass
    
    # ==================== 控制器回调设置 ====================
    
    def setup_controller_callbacks(self):
        """设置控制器回调函数"""
        self.controller.set_callback('on_status_update', self.update_status)
        self.controller.set_callback('on_progress_update', self.update_progress)
        self.controller.set_callback('on_log_message', self.add_log_message)
        self.controller.set_callback('on_env_list_update', self.update_env_list)
        self.controller.set_callback('on_video_count_update', self.update_video_count)
        self.controller.set_callback('on_upload_finished', self.upload_finished)
    
    # ==================== 界面更新方法 ====================
    
    def update_status(self, status_text):
        """更新状态显示"""
        self.root.after(0, lambda: self.status_var.set(status_text))
    
    def update_progress(self, progress_value):
        """更新进度条"""
        self.root.after(0, lambda: self.progress_var.set(progress_value))
    
    def add_log_message(self, message):
        """添加日志消息"""
        def _add_log():
            self.log_text.insert(tk.END, message + "\n")
            self.log_text.see(tk.END)
        self.root.after(0, _add_log)
    
    def update_env_list(self, env_list, status_msg):
        """更新环境列表显示"""
        def _update():
            # 更新状态
            self.hubstudio_status_var.set(status_msg)
            if env_list:
                available_count = sum(1 for env in env_list if "已关闭" in env)
                self.api_status_var.set("已连接")
                self.env_count_var.set(f"{len(env_list)} 个 (可用: {available_count})")
            else:
                self.api_status_var.set("未连接")
                self.env_count_var.set("0 个")
            
            # 更新环境列表
            self.env_list_text.config(state='normal')
            self.env_list_text.delete('1.0', tk.END)
            
            if env_list:
                for i, env in enumerate(env_list, 1):
                    self.env_list_text.insert(tk.END, f"{i}. {env}\n")
            else:
                self.env_list_text.insert(tk.END, "暂无可用环境\n请检查HubStudio连接状态")
            
            self.env_list_text.config(state='disabled')
        
        self.root.after(0, _update)
    
    def update_video_count(self, count, file_path=""):
        """更新视频计数显示"""
        def _update():
            if count > 0:
                video_name = os.path.basename(file_path)
                self.video_count_var.set(f"已选择: {video_name}")
                self.video_path_var.set(file_path)
            else:
                self.video_count_var.set("未选择视频")
                self.video_path_var.set("")
            
            # 更新上传按钮状态
            if count > 0 and not self.controller.is_upload_in_progress():
                self.upload_btn.config(state='normal')
            else:
                self.upload_btn.config(state='disabled')
        
        self.root.after(0, _update)
    
    def upload_finished(self, success):
        """上传完成处理"""
        def _finish():
            self.stop_btn.config(state='disabled')
            self.progress_var.set(0)
            
            # 更新上传按钮状态
            if self.controller.get_video_count() > 0:
                self.upload_btn.config(state='normal')
            
            if success:
                self.status_var.set("✅ 上传完成")
                messagebox.showinfo("成功", "视频上传完成！")
            else:
                self.status_var.set("❌ 上传失败")
                messagebox.showerror("失败", "视频上传失败！")
        
        self.root.after(0, _finish)
    
    def update_logs(self):
        """更新日志显示"""
        messages = self.controller.get_log_messages()
        for message in messages:
            self.log_text.insert(tk.END, message + "\n")
            self.log_text.see(tk.END)
        
        # 每100ms检查一次
        self.root.after(100, self.update_logs)
    
    # ==================== 事件处理方法 ====================
    
    def on_select_video(self):
        """选择视频文件事件"""
        file_types = [
            ("视频文件", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv *.webm"),
            ("MP4文件", "*.mp4"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择视频文件（只能选择一个）",
            filetypes=file_types
        )

        if file_path:
            self.controller.add_video_file(file_path)
    
    def on_clear_video(self):
        """清除视频选择事件"""
        self.controller.clear_video_selection()
    
    def on_start_upload(self):
        """开始上传事件"""
        if self.controller.is_upload_in_progress():
            messagebox.showwarning("警告", "上传正在进行中...")
            return
        
        if self.controller.get_video_count() == 0:
            messagebox.showwarning("警告", "请先添加视频文件")
            return
        
        # 获取配置参数
        api_url = self.api_url_var.get().strip()
        api_id = self.api_id_var.get().strip()
        api_secret = self.api_secret_var.get().strip()
        concurrent_count = self.concurrent_count_var.get()
        title = self.title_var.get().strip()
        description = self.desc_text.get('1.0', tk.END).strip()
        children_content = self.children_content_var.get() == "是"
        
        # 检查必要参数
        if not api_id or not api_secret:
            messagebox.showwarning("警告", "请先配置API凭证")
            return
        
        # 确认开始上传
        video_list = self.controller.get_video_list()
        if video_list:
            video_name = os.path.basename(video_list[0])
            if messagebox.askyesno("确认", 
                                  f"将使用 {concurrent_count} 个浏览器环境并发上传同一个视频\n"
                                  f"视频文件: {video_name}\n"
                                  f"确定要开始上传吗？"):
                # 更新按钮状态
                self.upload_btn.config(state='disabled')
                self.stop_btn.config(state='normal')
                
                # 开始上传
                self.controller.start_upload(api_url, api_id, api_secret, concurrent_count, 
                                           title, description, children_content)
    
    def on_stop_upload(self):
        """停止上传事件"""
        self.controller.stop_upload()
    
    def on_save_config(self):
        """保存配置事件"""
        try:
            # 更新配置
            self.controller.update_config('BROWSER', 'api_url', self.api_url_var.get())
            self.controller.update_config('HUBSTUDIO', 'api_id', self.api_id_var.get())
            self.controller.update_config('HUBSTUDIO', 'api_secret', self.api_secret_var.get())
            self.controller.update_config('VIDEO', 'default_title', self.title_var.get())
            
            # 获取描述文本
            description = self.desc_text.get('1.0', tk.END).strip()
            self.controller.update_config('VIDEO', 'default_description', description)
            
            # 保存高级设置
            self.controller.update_config('AUTOMATION', 'upload_interval', self.upload_interval_var.get())
            self.controller.update_config('AUTOMATION', 'wait_timeout', self.timeout_var.get())
            
            # 保存配置文件
            if self.controller.save_config():
                messagebox.showinfo("成功", "配置已保存成功")
            else:
                messagebox.showerror("错误", "保存配置失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def on_test_connection(self):
        """测试连接事件"""
        api_url = self.api_url_var.get().strip()
        api_id = self.api_id_var.get().strip()
        api_secret = self.api_secret_var.get().strip()
        
        if not api_id or not api_secret:
            messagebox.showwarning("警告", "请先配置API凭证")
            return
        
        self.controller.test_hubstudio_connection(api_url, api_id, api_secret)
    
    def on_refresh_environments(self):
        """刷新环境事件"""
        api_url = self.api_url_var.get().strip()
        api_id = self.api_id_var.get().strip()
        api_secret = self.api_secret_var.get().strip()
        
        self.controller.refresh_environments(api_url, api_id, api_secret)
    
    def on_show_help(self):
        """显示帮助事件"""
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("600x500")
        help_window.resizable(True, True)

        # 创建滚动文本框
        text_frame = ttk.Frame(help_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Microsoft YaHei', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 帮助内容
        help_content = """HubStudio + YouTube 自动化上传工具

快速开始：
1. 确保HubStudio客户端正在运行
2. 在HubStudio中创建Chrome内核环境
3. 添加要上传的视频文件
4. 点击开始上传

环境设置：
- 只支持Chrome内核环境
- 确保环境状态为已关闭
- 不支持Firebrowser内核

常见问题：
连接失败：检查HubStudio是否运行
未发现环境：创建Chrome内核环境
环境使用中：关闭正在运行的环境

支持格式：
MP4, AVI, MOV, MKV, FLV, WMV, WEBM

操作说明：
- 点击测试连接检查状态
- 查看日志获取详细信息
- 支持批量上传和进度跟踪"""

        text_widget.insert(tk.END, help_content)
        text_widget.config(state=tk.DISABLED)

        # 关闭按钮
        close_btn = ttk.Button(help_window, text="关闭", command=help_window.destroy)
        close_btn.pack(pady=10)
    
    def on_clear_logs(self):
        """清除日志事件"""
        self.log_text.delete(1.0, tk.END)
        self.controller.clear_logs()
