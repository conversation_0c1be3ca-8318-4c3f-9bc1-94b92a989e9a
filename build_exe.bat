@echo off
chcp 65001 >nul
echo ========================================
echo HubStudio + YouTube 自动化上传工具
echo 编译打包脚本
echo ========================================
echo.

echo [1/6] 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误：Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo [2/6] 检查PyInstaller...
python -c "import PyInstaller; print('PyInstaller版本:', PyInstaller.__version__)"
if %errorlevel% neq 0 (
    echo 错误：PyInstaller未安装
    echo 正在安装PyInstaller...
    pip install pyinstaller
)

echo [3/6] 清理旧的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "gui\__pycache__" rmdir /s /q "gui\__pycache__"

echo [4/6] 创建必要的目录...
if not exist "videos" mkdir "videos"
if not exist "logs" mkdir "logs"
if not exist "screenshots" mkdir "screenshots"

echo [5/6] 开始编译GUI版本...
echo 正在编译GUI版本，请稍候...
pyinstaller --clean hubstudio_gui.spec
if %errorlevel% neq 0 (
    echo 错误：GUI版本编译失败
    pause
    exit /b 1
)

echo [6/6] 开始编译命令行版本...
echo 正在编译命令行版本，请稍候...
pyinstaller --clean hubstudio_cli.spec
if %errorlevel% neq 0 (
    echo 错误：命令行版本编译失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 编译完成！
echo ========================================
echo GUI版本: dist\HubStudio_YouTube_Uploader_GUI.exe
echo 命令行版本: dist\HubStudio_YouTube_Uploader_CLI.exe
echo.
echo 文件大小:
dir /s dist\*.exe
echo.
echo 请在干净的Windows环境中测试exe文件
echo ========================================
pause
