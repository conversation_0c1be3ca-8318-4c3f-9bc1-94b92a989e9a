# HubStudio + YouTube 自动化上传工具 - 独立可执行版本

## 📦 打包信息

本工具已成功编译为独立的Windows可执行文件，无需安装Python环境即可运行。

### 生成的文件
- **GUI版本**: `HubStudio_YouTube_Uploader_GUI.exe` (约80MB)
- **命令行版本**: `HubStudio_YouTube_Uploader_CLI.exe` (约77MB)

## 🚀 快速开始

### 1. 系统要求
- Windows 10/11 (64位)
- HubStudio 指纹浏览器客户端
- 已登录YouTube的浏览器环境

### 2. 部署步骤

#### 方法一：完整部署（推荐）
1. 将整个项目文件夹复制到目标机器
2. 确保以下文件和目录存在：
   ```
   项目目录/
   ├── HubStudio_YouTube_Uploader_GUI.exe    # GUI版本
   ├── HubStudio_YouTube_Uploader_CLI.exe    # 命令行版本
   ├── config.ini                           # 配置文件
   ├── logs/                                 # 日志目录
   ├── screenshots/                          # 截图目录
   └── videos/                               # 视频目录
   ```

#### 方法二：最小部署
1. 只复制exe文件到目标机器
2. 首次运行时会自动创建必要的目录和配置文件

### 3. 使用方法

#### GUI版本使用
```bash
# 双击运行或命令行启动
HubStudio_YouTube_Uploader_GUI.exe
```

#### 命令行版本使用
```bash
# 批量上传（使用默认视频文件夹）
HubStudio_YouTube_Uploader_CLI.exe

# 上传单个视频文件
HubStudio_YouTube_Uploader_CLI.exe "C:\path\to\video.mp4"

# 批量上传指定文件夹
HubStudio_YouTube_Uploader_CLI.exe "C:\path\to\video\folder"
```

## ⚙️ 配置说明

### config.ini 配置文件
```ini
[BROWSER]
api_url = http://127.0.0.1:6873          # HubStudio API地址
profile_id = 1281247135                   # 默认浏览器环境ID

[HUBSTUDIO]
api_id = 你的API_ID                       # HubStudio API ID
api_secret = 你的API_SECRET               # HubStudio API Secret

[VIDEO]
video_folder = ./videos                   # 默认视频文件夹
default_tags = 自动化,HubStudio,YouTube    # 默认标签

[AUTOMATION]
wait_timeout = 30                         # 等待超时时间
upload_timeout = 600                      # 上传超时时间
retry_count = 3                           # 重试次数
```

## 🔧 故障排除

### 常见问题

#### ❌ "找不到HubStudio环境"
**原因**: HubStudio客户端未运行或API配置错误
**解决**: 
1. 确保HubStudio客户端正在运行
2. 检查config.ini中的API配置
3. 确保至少有一个Chrome浏览器环境

#### ❌ "连接失败"
**原因**: HubStudio API连接问题
**解决**:
1. 检查HubStudio客户端是否正常运行
2. 确认API地址是否正确（默认：http://127.0.0.1:6873）
3. 检查防火墙设置

#### ❌ "视频上传失败"
**原因**: YouTube登录状态或网络问题
**解决**:
1. 在HubStudio环境中手动登录YouTube
2. 检查网络连接
3. 查看logs目录下的日志文件

### 日志文件
- 位置: `logs/automation.log`
- 包含详细的运行日志和错误信息
- 可用于问题诊断

### 截图文件
- 位置: `screenshots/`
- 自动保存错误时的浏览器截图
- 帮助诊断上传过程中的问题

## 📋 功能特性

### ✅ 已实现功能
- [x] 独立可执行文件，无需Python环境
- [x] GUI和命令行双版本
- [x] 自动连接HubStudio浏览器环境
- [x] 支持单个和批量视频上传
- [x] 完整的错误处理和重试机制
- [x] 详细的日志记录
- [x] 自动截图保存错误状态
- [x] 支持自定义视频标题、描述、标签
- [x] 儿童内容设置
- [x] 并发上传支持

### 🔄 运行流程
1. 启动程序
2. 自动检测HubStudio环境
3. 连接到指定的浏览器环境
4. 导航到YouTube Studio
5. 执行视频上传流程
6. 自动填写视频信息
7. 发布视频
8. 记录结果和日志

## 🛡️ 安全说明

- exe文件已通过PyInstaller打包，包含所有必要的依赖
- 不包含恶意代码，可安全运行
- 建议在运行前进行病毒扫描（某些杀毒软件可能误报）
- 配置文件中的API密钥请妥善保管

## 📞 技术支持

如遇到问题，请提供以下信息：
1. 错误截图
2. logs/automation.log 日志文件
3. 系统环境信息
4. HubStudio版本信息

---

**注意**: 本工具专为HubStudio + YouTube组合优化，确保在使用前已正确配置HubStudio环境并登录YouTube账号。
