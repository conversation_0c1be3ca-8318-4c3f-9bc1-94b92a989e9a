#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一视频上传模块
整合视频处理和YouTube上传功能，专注于视频上传
移除视频审核功能，只要是视频文件就可以上传
"""

import os
import time
from pathlib import Path
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from loguru import logger


class VideoUploaderUnified:
    """统一的视频上传器，整合视频处理和YouTube上传功能"""
    
    def __init__(self, driver, config):
        self.driver = driver
        self.config = config
        self.wait = WebDriverWait(driver, 30)
        self.upload_url = config.get('YOUTUBE', 'upload_url', fallback='https://studio.youtube.com')
        
        # 视频处理相关配置
        self.supported_formats = self._get_supported_formats()
        self.max_file_size = int(config.get('VIDEO', 'max_file_size', fallback=2048)) * 1024 * 1024  # 转换为字节
        
    def _get_supported_formats(self):
        """获取支持的视频格式"""
        formats_str = self.config.get('VIDEO', 'supported_formats', fallback='.mp4,.avi,.mov,.mkv,.flv,.wmv,.webm')
        return [fmt.strip().lower() for fmt in formats_str.split(',')]
    
    # ==================== 视频处理功能 ====================
    
    def scan_video_folder(self, folder_path):
        """扫描视频文件夹"""
        try:
            logger.info(f"扫描视频文件夹: {folder_path}")
            
            if not os.path.exists(folder_path):
                logger.error(f"文件夹不存在: {folder_path}")
                return []
            
            video_files = []
            folder = Path(folder_path)
            
            # 扫描所有支持的视频文件
            for file_path in folder.iterdir():
                if file_path.is_file():
                    file_ext = file_path.suffix.lower()
                    if file_ext in self.supported_formats:
                        video_info = self.get_video_info(str(file_path))
                        if video_info:
                            video_files.append(video_info)
            
            logger.info(f"找到 {len(video_files)} 个视频文件")
            return video_files
            
        except Exception as e:
            logger.error(f"扫描视频文件夹失败: {e}")
            return []
    
    def get_video_info(self, video_path):
        """获取视频基本信息（简化版，不进行复杂审核）"""
        try:
            file_path = Path(video_path)
            
            if not file_path.exists():
                logger.error(f"视频文件不存在: {video_path}")
                return None
            
            # 检查文件格式
            file_ext = file_path.suffix.lower()
            if file_ext not in self.supported_formats:
                logger.warning(f"不支持的视频格式: {file_ext}")
                return None
            
            # 获取文件大小
            file_size = file_path.stat().st_size
            
            # 检查文件大小（可选，如果文件过大只警告不拒绝）
            if file_size > self.max_file_size:
                logger.warning(f"文件较大: {file_size / (1024*1024):.1f}MB > {self.max_file_size / (1024*1024):.1f}MB，但仍可上传")
            
            # 获取文件修改时间
            mtime = file_path.stat().st_mtime
            modified_time = datetime.fromtimestamp(mtime)
            
            video_info = {
                'filename': file_path.name,
                'filepath': str(file_path.absolute()),
                'size': file_size,
                'size_mb': round(file_size / (1024 * 1024), 2),
                'format': file_ext,
                'modified_time': modified_time,
                'status': 'ready'  # 简化状态，只要是视频就标记为ready
            }
            
            logger.debug(f"视频信息: {video_info['filename']} ({video_info['size_mb']}MB)")
            return video_info
            
        except Exception as e:
            logger.error(f"获取视频信息失败: {e}")
            return None
    
    def validate_video_file(self, video_path):
        """验证视频文件（简化版）"""
        try:
            video_info = self.get_video_info(video_path)
            if video_info:
                logger.info(f"视频文件验证通过: {video_info['filename']}")
                return True
            else:
                logger.error(f"视频文件验证失败: {video_path}")
                return False
                
        except Exception as e:
            logger.error(f"验证视频文件异常: {e}")
            return False
    
    def generate_video_title(self, video_path):
        """生成视频标题"""
        try:
            file_path = Path(video_path)
            filename = file_path.stem  # 不包含扩展名的文件名
            
            # 使用配置的标题模板
            title_template = self.config.get('VIDEO', 'title_template', fallback='{filename} - {date}')
            current_date = datetime.now().strftime('%Y-%m-%d')
            
            title = title_template.format(
                filename=filename,
                date=current_date
            )
            
            return title
            
        except Exception as e:
            logger.error(f"生成视频标题失败: {e}")
            return os.path.basename(video_path)
    
    def generate_video_description(self, video_path):
        """生成视频描述"""
        try:
            file_path = Path(video_path)
            filename = file_path.name
            
            # 使用配置的描述模板
            desc_template = self.config.get('VIDEO', 'description_template', 
                                           fallback='Video file: {filename}\nUpload time: {date} {time}')
            current_date = datetime.now().strftime('%Y-%m-%d')
            current_time = datetime.now().strftime('%H:%M:%S')
            
            description = desc_template.format(
                filename=filename,
                date=current_date,
                time=current_time
            )
            
            return description
            
        except Exception as e:
            logger.error(f"生成视频描述失败: {e}")
            return f"Video file: {os.path.basename(video_path)}"
    
    def get_supported_formats_string(self):
        """获取支持格式的字符串表示"""
        return ', '.join(self.supported_formats)
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    # ==================== 浏览器验证功能 ====================

    def _verify_browser_connection(self):
        """验证浏览器连接是否正常"""
        try:
            if not self.driver:
                logger.error("❌ 浏览器驱动为空")
                return False

            # 尝试获取当前URL
            try:
                current_url = self.driver.current_url
                logger.debug(f"✅ 浏览器连接正常，当前URL: {current_url}")
            except Exception as e:
                logger.error(f"❌ 无法获取当前URL: {e}")
                return False

            # 尝试执行简单的JavaScript
            try:
                result = self.driver.execute_script("return 'browser_test_ok';")
                if result != 'browser_test_ok':
                    logger.error("❌ JavaScript执行测试失败")
                    return False
            except Exception as e:
                logger.error(f"❌ JavaScript执行失败: {e}")
                return False

            # 检查浏览器窗口是否存在
            try:
                window_handles = self.driver.window_handles
                if not window_handles:
                    logger.error("❌ 没有可用的浏览器窗口")
                    return False
                logger.debug(f"✅ 浏览器窗口检查通过，窗口数: {len(window_handles)}")
            except Exception as e:
                logger.error(f"❌ 浏览器窗口检查失败: {e}")
                return False

            logger.debug("✅ 浏览器连接验证通过")
            return True

        except Exception as e:
            logger.error(f"❌ 浏览器连接验证异常: {e}")
            return False

    def _verify_upload_success(self):
        """验证视频上传是否真正成功"""
        try:
            logger.info("🔍 验证上传成功状态...")

            # 等待页面稳定
            time.sleep(3)

            # 检查当前URL是否包含YouTube相关内容
            try:
                current_url = self.driver.current_url.lower()
                if "youtube.com" not in current_url and "youtu.be" not in current_url:
                    logger.warning(f"⚠️ 当前页面不是YouTube页面: {current_url}")
                    return False
            except Exception as e:
                logger.error(f"❌ 无法获取当前URL: {e}")
                return False

            # 检查页面内容，寻找成功指示器
            success_indicators = [
                "已发布", "已發布", "Published", "Video published",
                "影片已发布", "影片已發布", "Video is live",
                "处理完成", "處理完成", "Processing complete",
                "youtu.be/", "youtube.com/watch"
            ]

            try:
                page_text = self.driver.execute_script("return document.body.innerText || '';")
                page_html = self.driver.page_source.lower()

                success_found = False
                for indicator in success_indicators:
                    if indicator.lower() in page_text.lower() or indicator.lower() in page_html:
                        logger.info(f"✅ 发现成功指示器: {indicator}")
                        success_found = True
                        break

                if success_found:
                    logger.info("🎉 上传成功验证通过")
                    return True
                else:
                    logger.warning("⚠️ 未发现明确的成功指示器")
                    # 即使没有明确指示器，如果没有错误信息也认为可能成功
                    error_indicators = [
                        "error", "错误", "錯誤", "failed", "失败", "失敗",
                        "upload failed", "上传失败", "上傳失敗"
                    ]

                    error_found = False
                    for error_indicator in error_indicators:
                        if error_indicator.lower() in page_text.lower():
                            logger.error(f"❌ 发现错误指示器: {error_indicator}")
                            error_found = True
                            break

                    if not error_found:
                        logger.info("✅ 未发现错误指示器，认为上传可能成功")
                        return True
                    else:
                        logger.error("❌ 发现错误指示器，上传可能失败")
                        return False

            except Exception as e:
                logger.error(f"❌ 页面内容检查失败: {e}")
                return False

        except Exception as e:
            logger.error(f"❌ 上传成功验证异常: {e}")
            return False

    # ==================== YouTube上传功能 ====================
    
    def upload_video(self, video_path, title="", description="", children_content=False, tags=None):
        """
        完整的视频上传流程

        Args:
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            children_content: 是否为儿童内容
            tags: 视频标签列表

        Returns:
            bool: 上传是否成功
        """
        try:
            logger.info(f"🎬 开始上传视频: {os.path.basename(video_path)}")

            # 关键检查：验证浏览器驱动是否可用
            if not self._verify_browser_connection():
                logger.error("❌ 浏览器连接验证失败，无法继续上传")
                return False

            # 首先验证视频文件
            if not self.validate_video_file(video_path):
                logger.error("❌ 视频文件验证失败，取消上传")
                return False

            # 如果没有提供标题和描述，自动生成
            if not title:
                title = self.generate_video_title(video_path)
                logger.info(f"📝 自动生成标题: {title}")

            if not description:
                description = self.generate_video_description(video_path)
                logger.info(f"📄 自动生成描述: {description[:50]}...")

            # 步骤1: 导航到 YouTube Studio
            logger.info("🌐 步骤1: 导航到YouTube Studio")
            if not self._navigate_to_studio():
                logger.error("❌ 导航到YouTube Studio失败")
                return False

            # 步骤2: 点击上传按钮
            logger.info("🔘 步骤2: 点击上传按钮")
            if not self._click_upload_button():
                logger.error("❌ 点击上传按钮失败")
                return False

            # 步骤3: 选择并上传文件
            logger.info("📁 步骤3: 上传视频文件")
            if not self._upload_file(video_path):
                logger.error("❌ 上传视频文件失败")
                return False

            # 步骤4: 填写详细资讯
            logger.info("📝 步骤4: 填写视频详细信息")
            if not self._fill_details(title, description, children_content, tags):
                logger.error("❌ 填写视频详细信息失败")
                return False

            # 步骤5: 跳过影片元素页面
            logger.info("⏭️ 步骤5: 跳过影片元素页面")
            if not self._skip_video_elements():
                logger.error("❌ 跳过影片元素页面失败")
                return False

            # 步骤6: 跳过检查项目页面
            logger.info("✅ 步骤6: 跳过检查项目页面")
            if not self._skip_checks():
                logger.error("❌ 跳过检查项目页面失败")
                return False

            # 步骤7: 发布视频
            logger.info("🚀 步骤7: 发布视频")
            if not self._publish_video():
                logger.error("❌ 发布视频失败")
                return False

            # 最终验证：确认上传真正成功
            if not self._verify_upload_success():
                logger.error("❌ 上传成功验证失败")
                return False

            logger.info("🎉 视频上传流程完成！")
            return True

        except Exception as e:
            logger.error(f"❌ 上传视频过程中发生异常: {e}")
            import traceback
            logger.debug(f"详细错误: {traceback.format_exc()}")
            self._take_screenshot("upload_error")
            return False
    
    def _navigate_to_studio(self):
        """导航到 YouTube Studio"""
        try:
            logger.info("导航到 YouTube Studio")

            # 首先尝试导航到主页
            studio_url = "https://studio.youtube.com"
            self.driver.get(studio_url)

            # 等待页面加载
            time.sleep(5)

            # 检查是否需要登录
            current_url = self.driver.current_url
            if "accounts.google.com" in current_url:
                logger.error("需要先登录 Google 账号")
                logger.info("请在浏览器中手动登录 YouTube，然后重试")
                return False

            # 检查是否成功到达 YouTube Studio
            if "studio.youtube.com" not in current_url:
                logger.warning(f"未到达 YouTube Studio，当前URL: {current_url}")
                # 尝试重新导航
                self.driver.get(studio_url)
                time.sleep(3)

            # 等待页面完全加载
            try:
                # 等待页面标题或关键元素出现
                WebDriverWait(self.driver, 10).until(
                    lambda driver: "studio" in driver.title.lower() or
                                 len(driver.find_elements(By.TAG_NAME, "ytcp-app")) > 0
                )
                logger.info("✅ 成功导航到 YouTube Studio")
            except:
                logger.warning("页面加载可能不完整，但继续执行")

            # 保存截图用于调试
            self._take_screenshot("navigate_to_studio")

            return True

        except Exception as e:
            logger.error(f"导航到 YouTube Studio 失败: {e}")
            self._take_screenshot("navigate_error")
            return False
    
    def _click_upload_button(self):
        """点击上传按钮"""
        try:
            logger.info("查找并点击上传按钮")

            # 等待页面完全加载
            time.sleep(3)

            # 先尝试直接导航到上传页面
            upload_urls = [
                "https://studio.youtube.com/channel/upload",
                "https://www.youtube.com/upload",
                "https://studio.youtube.com/upload"
            ]

            for upload_url in upload_urls:
                try:
                    logger.info(f"尝试直接导航到: {upload_url}")
                    self.driver.get(upload_url)
                    time.sleep(3)

                    # 检查是否成功到达上传页面
                    current_url = self.driver.current_url.lower()
                    if "upload" in current_url and "studio.youtube.com" in current_url:
                        logger.info("✅ 成功直接导航到上传页面")
                        return True
                except Exception as e:
                    logger.debug(f"导航到 {upload_url} 失败: {e}")
                    continue

            logger.warning("直接导航失败，尝试点击按钮")

            # 如果直接导航失败，尝试查找并点击上传按钮
            logger.info("尝试查找上传按钮...")

            # 更全面的上传按钮选择器 - 2024年最新版本
            upload_selectors = [
                # 最新的创建按钮选择器
                "button[id='create-icon']",
                "ytcp-button[id='create-icon']",
                "#create-icon",
                "button#create-icon",

                # 通过 aria-label 查找
                "button[aria-label*='Create']",
                "button[aria-label*='建立']",
                "button[aria-label*='Upload']",
                "button[aria-label*='上传']",

                # 通过类名查找
                "button[class*='create']",
                "button[class*='upload']",
                "ytcp-icon-button[class*='create']",

                # XPath 选择器
                "//button[@id='create-icon']",
                "//ytcp-button[@id='create-icon']",
                "//button[contains(@aria-label, 'Create')]",
                "//button[contains(@aria-label, '建立')]",
                "//button[contains(@aria-label, 'Upload')]",
                "//button[contains(@aria-label, '上传')]",

                # 通过图标查找
                "//button[.//tp-yt-iron-icon[@icon='add']]",
                "//button[.//yt-icon[@class*='add']]",
                "//button[.//yt-icon-button[@icon='add']]",

                # 通过文本内容查找
                "//button[contains(text(), 'Create')]",
                "//button[contains(text(), '建立')]",
                "//button[contains(text(), 'Upload')]",
                "//button[contains(text(), '上传')]",
                "//span[contains(text(), 'Create')]//ancestor::button",
                "//span[contains(text(), '建立')]//ancestor::button",

                # 更具体的 YouTube Studio 选择器
                "//ytcp-app//button[@id='create-icon']",
                "//ytcp-app//ytcp-button[@id='create-icon']",
                "//ytcp-app-header//button[@id='create-icon']",
                "//ytcp-app-header//ytcp-button[@id='create-icon']",

                # 备用选择器
                "button[title*='Create']",
                "button[title*='建立']",
                "button[title*='Upload']",
                "button[title*='上传']"
            ]

            upload_button = None

            # 首先尝试快速查找常用选择器
            quick_selectors = [
                "button[id='create-icon']",
                "#create-icon",
                "//button[@id='create-icon']"
            ]

            for selector in quick_selectors:
                try:
                    if selector.startswith("//"):
                        upload_button = WebDriverWait(self.driver, 3).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        upload_button = WebDriverWait(self.driver, 3).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    logger.info(f"✅ 快速找到上传按钮: {selector}")
                    break
                except:
                    continue

            # 如果快速查找失败，使用完整列表
            if not upload_button:
                logger.info("快速查找失败，使用完整选择器列表...")

                for i, selector in enumerate(upload_selectors):
                    try:
                        logger.debug(f"尝试选择器 {i+1}/{len(upload_selectors)}: {selector}")

                        if selector.startswith("//"):
                            upload_button = WebDriverWait(self.driver, 2).until(
                                EC.element_to_be_clickable((By.XPATH, selector))
                            )
                        else:
                            upload_button = WebDriverWait(self.driver, 2).until(
                                EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                            )

                        logger.info(f"✅ 找到上传按钮: {selector}")
                        break

                    except TimeoutException:
                        continue
                    except Exception as e:
                        logger.debug(f"选择器 {selector} 失败: {e}")
                        continue

            if not upload_button:
                logger.error("❌ 未找到上传按钮")
                self._take_screenshot("no_upload_button")
                return False

            # 点击上传按钮
            try:
                upload_button.click()
                logger.info("成功点击上传按钮")
            except Exception as e:
                # 如果普通点击失败，尝试 JavaScript 点击
                logger.warning(f"普通点击失败，尝试 JavaScript 点击: {e}")
                self.driver.execute_script("arguments[0].click();", upload_button)
                logger.info("JavaScript 点击成功")

            # 等待上传对话框或菜单出现
            time.sleep(3)

            # 查找"上传影片"选项（如果有下拉菜单）
            upload_video_selectors = [
                "//tp-yt-paper-item[contains(text(), '上传影片')]",
                "//tp-yt-paper-item[contains(text(), 'Upload video')]",
                "//div[contains(text(), '上传影片')]",
                "//div[contains(text(), 'Upload video')]",
                "//span[contains(text(), '上传影片')]",
                "//span[contains(text(), 'Upload video')]",
                "//a[contains(@href, 'upload')]",
                "//ytd-menu-service-item-renderer[contains(., '上传影片')]",
                "//ytd-menu-service-item-renderer[contains(., 'Upload video')]"
            ]

            for selector in upload_video_selectors:
                try:
                    upload_video_option = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    upload_video_option.click()
                    logger.info("成功点击上传影片选项")
                    time.sleep(2)
                    return True
                except TimeoutException:
                    continue

            # 检查是否已经在上传页面
            if "upload" in self.driver.current_url.lower():
                logger.info("已经在上传页面")
                return True

            logger.info("未找到上传影片选项，但继续执行")
            return True

        except Exception as e:
            logger.error(f"点击上传按钮失败: {e}")
            self._take_screenshot("upload_button_error")
            return False
    
    def _upload_file(self, video_path):
        """上传文件"""
        try:
            logger.info(f"🚀 开始上传文件: {video_path}")

            # 验证文件存在
            absolute_path = os.path.abspath(video_path)
            if not os.path.exists(absolute_path):
                logger.error(f"❌ 文件不存在: {absolute_path}")
                return False

            logger.info(f"✅ 文件路径: {absolute_path}")

            # 等待页面加载
            time.sleep(3)

            # 直接查找所有文件输入并尝试
            logger.info("🔍 查找文件输入元素...")

            file_inputs = self.driver.execute_script("""
                return Array.from(document.querySelectorAll('input[type="file"]'));
            """)

            logger.info(f"找到 {len(file_inputs)} 个文件输入元素")

            # 直接尝试每个文件输入
            for i, file_input in enumerate(file_inputs):
                try:
                    logger.info(f"尝试文件输入 {i+1}")

                    # 直接发送文件路径
                    file_input.send_keys(absolute_path)
                    logger.info(f"✅ 文件已发送到输入 {i+1}")

                    # 等待一下看是否有反应
                    time.sleep(2)

                    # 简单检查是否开始上传
                    upload_started = self.driver.execute_script("""
                        const text = document.body.innerText || '';
                        return text.includes('上传') || text.includes('上傳') ||
                               text.includes('Uploading') || text.includes('处理') ||
                               text.includes('Processing');
                    """)

                    if upload_started:
                        logger.info("🎉 上传已开始！")
                        return True

                except Exception as e:
                    logger.warning(f"⚠️ 输入 {i+1} 失败: {e}")
                    continue

            # 如果没有文件输入，创建一个
            logger.info("🔧 创建文件输入元素...")

            created_input = self.driver.execute_script("""
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'video/*';
                input.style.position = 'fixed';
                input.style.top = '10px';
                input.style.left = '10px';
                input.style.zIndex = '9999';
                input.style.display = 'block';
                document.body.appendChild(input);

                input.addEventListener('change', function(e) {
                    console.log('文件已选择');
                    const youtubeInputs = document.querySelectorAll('input[type="file"]');
                    for (let ytInput of youtubeInputs) {
                        if (ytInput !== input) {
                            try {
                                ytInput.files = e.target.files;
                                ytInput.dispatchEvent(new Event('change', {bubbles: true}));
                                console.log('已触发YouTube输入');
                            } catch (err) {
                                console.log('触发失败:', err);
                            }
                        }
                    }
                });

                return input;
            """)

            if created_input:
                try:
                    created_input.send_keys(absolute_path)
                    logger.info("✅ 文件已发送到创建的输入")
                    time.sleep(3)
                    return True
                except Exception as e:
                    logger.warning(f"⚠️ 创建的输入失败: {e}")

            logger.error("❌ 所有方法都失败了")
            return False
            
        except Exception as e:
            logger.error(f"上传文件失败: {e}")
            return False

    def _fill_details(self, title, description, children_content, tags=None):
        """填写视频详细资讯"""
        try:
            logger.info("填写视频详细资讯")

            # 快速等待页面加载
            time.sleep(1.5)  # 减少等待时间

            # 保存当前状态截图
            self._take_screenshot("details_page_start")

            # 填写标题 - 高效选择器优先
            if title:
                logger.info(f"设置标题: {title}")
                success = self._set_input_field("title", title, [
                    # 最有效的选择器放在前面
                    "//div[@id='textbox']//div[@contenteditable='true']",
                    "//ytcp-social-suggestions-textbox[@label='标题']//div[@contenteditable='true']",
                    "//ytcp-social-suggestions-textbox[@label='Title']//div[@contenteditable='true']",
                    "(//div[@contenteditable='true'])[1]",
                    "//div[@role='textbox' and @contenteditable='true']",
                    "//div[@aria-label='标题（必填）']//div[@contenteditable='true']",
                    "//div[@aria-label='Title (required)']//div[@contenteditable='true']",
                    "//div[@aria-label='标题']//div[@contenteditable='true']",
                    "//div[@aria-label='Title']//div[@contenteditable='true']"
                ])
                
                if not success:
                    logger.warning("⚠️ 标题设置失败，尝试调试...")
                    self._debug_editable_elements("title")



            # 填写描述 - 高效选择器优先
            if description:
                success = self._set_input_field("描述", description, [
                    # 最有效的选择器放在前面
                    "(//div[@contenteditable='true'])[2]",
                    "//div[@role='textbox' and @contenteditable='true'][2]",
                    "//ytcp-social-suggestions-textbox[@label='说明']//div[@contenteditable='true']",
                    "//ytcp-social-suggestions-textbox[@label='Description']//div[@contenteditable='true']",
                    "//div[@aria-label='说明']//div[@contenteditable='true']",
                    "//div[@aria-label='Description']//div[@contenteditable='true']",
                    "//div[@aria-label='描述']//div[@contenteditable='true']"
                ])
                
                if not success:
                    logger.warning("⚠️ 描述设置失败，尝试调试...")
                    self._debug_editable_elements("description")

            # 设置儿童内容选项 - 优化版本
            logger.info(f"设置儿童内容选项: {'是' if children_content else '否'}")
            
            # 等待页面加载
            time.sleep(2)
            
            # 滚动到页面中部，确保儿童内容选项可见
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(1)
            
            # 截图调试
            self._take_screenshot("before_kids_content")
            
            # 设置儿童内容选项
            success = self._set_children_content_option(children_content)
            
            if not success:
                logger.warning("⚠️ 儿童内容选项设置失败，尝试调试...")
                self._debug_editable_elements("children_content")

            # 截图确认设置
            self._take_screenshot("after_kids_content")

            # 设置标签
            if tags:
                logger.info(f"设置标签: {tags}")
                self._set_tags(tags)

            # 保存设置后的截图
            self._take_screenshot("details_filled")

            # 点击"下一步"按钮进入影片元素页面
            logger.info("查找下一步按钮...")

            # 滚动到页面底部，确保下一步按钮可见
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            next_button_selectors = [
                "//button[@aria-label='下一步']",
                "//button[@aria-label='Next']",
                "//button[contains(text(), '下一步')]",
                "//button[contains(text(), 'Next')]",
                "//ytcp-button[contains(text(), '下一步')]",
                "//ytcp-button[contains(text(), 'Next')]",
                "#next-button",
                "//div[@id='next-button']//button",
                "//ytcp-button[@id='next-button']",
                "//button[contains(@class, 'next')]"
            ]

            next_clicked = False
            for selector in next_button_selectors:
                try:
                    if selector.startswith("//"):
                        next_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        next_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )

                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                    time.sleep(1)

                    # 尝试点击
                    try:
                        next_button.click()
                    except:
                        # 如果普通点击失败，使用JavaScript点击
                        self.driver.execute_script("arguments[0].click();", next_button)

                    logger.info("✅ 成功点击下一步按钮")
                    next_clicked = True
                    time.sleep(3)
                    break

                except Exception as e:
                    logger.debug(f"下一步选择器 {selector} 失败: {e}")
                    continue

            if not next_clicked:
                logger.error("❌ 未找到下一步按钮")
                self._take_screenshot("no_next_button")
                return False

            return True

        except Exception as e:
            logger.error(f"填写详细资讯失败: {e}")
            return False

    def _set_tags(self, tags):
        """设置视频标签"""
        try:
            logger.info(f"设置标签: {tags}")

            # 如果tags是字符串，转换为列表
            if isinstance(tags, str):
                tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
            else:
                tag_list = tags

            if not tag_list:
                logger.warning("没有有效的标签需要设置")
                return True

            # 查找标签输入框
            tag_selectors = [
                "//input[@aria-label='标签']",
                "//input[@aria-label='Tags']",
                "//input[@placeholder='添加标签']",
                "//input[@placeholder='Add tags']",
                "//ytcp-chip-bar//input",
                "//div[@id='tags']//input",
                "//div[contains(@class, 'tags')]//input"
            ]

            tag_input = None
            for selector in tag_selectors:
                try:
                    tag_input = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    break
                except:
                    continue

            if not tag_input:
                logger.warning("⚠️ 未找到标签输入框，跳过标签设置")
                return True

            # 设置标签
            for i, tag in enumerate(tag_list):
                try:
                    # 点击输入框
                    tag_input.click()
                    time.sleep(0.3)

                    # 输入标签
                    tag_input.send_keys(tag)
                    time.sleep(0.5)

                    # 按回车确认标签
                    tag_input.send_keys(Keys.ENTER)
                    time.sleep(0.5)

                    logger.info(f"✅ 已添加标签: {tag}")

                except Exception as e:
                    logger.warning(f"⚠️ 添加标签失败 '{tag}': {e}")
                    continue

            logger.info("✅ 标签设置完成")
            return True

        except Exception as e:
            logger.error(f"设置标签失败: {e}")
            return False

    def _skip_video_elements(self):
        """跳过影片元素页面"""
        try:
            logger.info("处理影片元素页面")

            # 等待页面加载
            time.sleep(5)
            
            # 保存当前页面截图
            self._take_screenshot("video_elements_page")

            # 滚动到页面底部，确保下一步按钮可见
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # 查找并点击"下一步"按钮
            next_button_selectors = [
                "//button[@aria-label='下一步']",
                "//button[@aria-label='Next']",
                "//button[contains(text(), '下一步')]",
                "//button[contains(text(), 'Next')]",
                "//ytcp-button[contains(text(), '下一步')]",
                "//ytcp-button[contains(text(), 'Next')]",
                "#next-button",
                "//div[@id='next-button']//button",
                "//ytcp-button[@id='next-button']",
                "//button[contains(@class, 'next')]",
                "//div[contains(@class, 'next')]//button",
                "//button[contains(@aria-label, 'next')]",
                "//button[contains(@aria-label, 'Next')]"
            ]

            next_clicked = False
            for selector in next_button_selectors:
                try:
                    if selector.startswith("//"):
                        next_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        next_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )

                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                    time.sleep(1)

                    # 尝试点击
                    try:
                        next_button.click()
                    except:
                        # 如果普通点击失败，使用JavaScript点击
                        self.driver.execute_script("arguments[0].click();", next_button)

                    logger.info("✅ 成功跳过影片元素页面")
                    next_clicked = True
                    time.sleep(3)
                    break

                except Exception as e:
                    logger.debug(f"影片元素页面下一步选择器 {selector} 失败: {e}")
                    continue

            if not next_clicked:
                logger.error("❌ 未找到影片元素页面的下一步按钮")
                self._take_screenshot("video_elements_no_next")
                return False

            return True

        except Exception as e:
            logger.error(f"跳过影片元素页面失败: {e}")
            self._take_screenshot("video_elements_error")
            return False

    def _skip_checks(self):
        """跳过检查项目页面"""
        try:
            logger.info("处理检查项目页面")

            # 等待页面加载
            time.sleep(5)
            
            # 保存当前页面截图
            self._take_screenshot("checks_page")

            # 滚动到页面底部，确保下一步按钮可见
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # 查找并点击"下一步"按钮
            next_button_selectors = [
                "//button[@aria-label='下一步']",
                "//button[@aria-label='Next']",
                "//button[contains(text(), '下一步')]",
                "//button[contains(text(), 'Next')]",
                "//ytcp-button[contains(text(), '下一步')]",
                "//ytcp-button[contains(text(), 'Next')]",
                "#next-button",
                "//div[@id='next-button']//button",
                "//ytcp-button[@id='next-button']",
                "//button[contains(@class, 'next')]",
                "//div[contains(@class, 'next')]//button",
                "//button[contains(@aria-label, 'next')]",
                "//button[contains(@aria-label, 'Next')]"
            ]

            next_clicked = False
            for selector in next_button_selectors:
                try:
                    if selector.startswith("//"):
                        next_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        next_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )

                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                    time.sleep(1)

                    # 尝试点击
                    try:
                        next_button.click()
                    except:
                        # 如果普通点击失败，使用JavaScript点击
                        self.driver.execute_script("arguments[0].click();", next_button)

                    logger.info("✅ 成功跳过检查项目页面")
                    next_clicked = True
                    time.sleep(3)
                    break

                except Exception as e:
                    logger.debug(f"检查项目页面下一步选择器 {selector} 失败: {e}")
                    continue

            if not next_clicked:
                logger.error("❌ 未找到检查项目页面的下一步按钮")
                self._take_screenshot("checks_no_next")
                return False

            return True

        except Exception as e:
            logger.error(f"跳过检查项目页面失败: {e}")
            self._take_screenshot("checks_error")
            return False

    def _publish_video(self):
        """发布视频 - 设置为公开并发布"""
        try:
            logger.info("处理瀏覽權限和发布视频")

            # 等待页面加载
            time.sleep(5)
            
            # 保存当前页面截图
            self._take_screenshot("publish_page")

            # 滚动到页面顶部，查看瀏覽權限设置
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
            # 检查并设置瀏覽權限为"公开"
            try:
                logger.info("设置瀏覽權限为公开")
                
                # 查找公开选项的选择器
                public_selectors = [
                    "//tp-yt-paper-radio-button[@name='PUBLIC']",
                    "//input[@name='PUBLIC']",
                    "//input[@value='PUBLIC']",
                    "//div[contains(text(), '公开') and not(contains(text(), '不公开'))]//ancestor::*//tp-yt-paper-radio-button",
                    "//span[contains(text(), '公开') and not(contains(text(), '不公开'))]//ancestor::*//tp-yt-paper-radio-button",
                    "//label[contains(text(), '公开') and not(contains(text(), '不公开'))]//tp-yt-paper-radio-button",
                    "//div[contains(text(), 'Public')]//ancestor::*//tp-yt-paper-radio-button",
                    "//span[contains(text(), 'Public')]//ancestor::*//tp-yt-paper-radio-button",
                    "//label[contains(text(), 'Public')]//tp-yt-paper-radio-button"
                ]
                
                visibility_set = False
                for selector in public_selectors:
                    try:
                        visibility_option = WebDriverWait(self.driver, 3).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        
                        # 滚动到元素位置
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", visibility_option)
                        time.sleep(0.5)
                        
                        # 尝试点击
                        try:
                            visibility_option.click()
                        except:
                            # 如果普通点击失败，使用JavaScript点击
                            self.driver.execute_script("arguments[0].click();", visibility_option)
                        
                        logger.info("✅ 设置瀏覽權限为公开")
                        visibility_set = True
                        time.sleep(1)
                        
                        # 截图确认设置
                        self._take_screenshot("visibility_set_public")
                        break
                        
                    except Exception as e:
                        logger.debug(f"公开选项选择器 {selector} 失败: {e}")
                        continue
                
                if not visibility_set:
                    logger.warning("⚠️ 未能设置瀏覽權限为公开，使用默认设置")
                    # 截图以便调试
                    self._take_screenshot("visibility_failed")
                    
            except Exception as e:
                logger.warning(f"设置瀏覽權限失败: {e}")

            # 滚动到页面底部，查找发布按钮
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # 查找发布按钮
            publish_button_selectors = [
                "//button[@aria-label='发布']",
                "//button[@aria-label='發布']",
                "//button[@aria-label='Publish']",
                "//button[contains(text(), '发布')]",
                "//button[contains(text(), '發布')]",
                "//button[contains(text(), 'Publish')]",
                "//button[contains(text(), 'PUBLISH')]",
                "//ytcp-button[contains(text(), '发布')]",
                "//ytcp-button[contains(text(), '發布')]",
                "//ytcp-button[contains(text(), 'Publish')]",
                "#publish-button",
                "//div[@id='publish-button']//button",
                "//ytcp-button[@id='publish-button']",
                "//button[contains(@class, 'publish')]",
                "//div[contains(@class, 'publish')]//button"
            ]

            publish_clicked = False
            for selector in publish_button_selectors:
                try:
                    if selector.startswith("//"):
                        publish_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        publish_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )

                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", publish_button)
                    time.sleep(1)

                    # 尝试点击
                    try:
                        publish_button.click()
                    except:
                        # 如果普通点击失败，使用JavaScript点击
                        self.driver.execute_script("arguments[0].click();", publish_button)

                    logger.info("✅ 成功点击发布按钮")
                    publish_clicked = True
                    break

                except Exception as e:
                    logger.debug(f"发布按钮选择器 {selector} 失败: {e}")
                    continue

            if not publish_clicked:
                logger.error("❌ 未找到发布按钮")
                self._take_screenshot("no_publish_button")
                return False

            # 等待发布完成
            logger.info("等待发布完成...")
            time.sleep(10)
            
            # 保存发布后的截图
            self._take_screenshot("after_publish")

            # 检查是否有成功提示或视频链接
            success_indicators = [
                "//div[contains(text(), '已发布') or contains(text(), '已發布')]",
                "//div[contains(text(), 'Published')]",
                "//a[contains(@href, 'youtu.be')]",
                "//a[contains(@href, 'youtube.com/watch')]",
                "//div[contains(text(), '影片连结') or contains(text(), '影片連結')]",
                "//div[contains(text(), 'Video link')]",
                "//span[contains(text(), '已发布') or contains(text(), '已發布')]",
                "//span[contains(text(), 'Published')]"
            ]

            success_found = False
            for indicator in success_indicators:
                try:
                    success_element = self.driver.find_element(By.XPATH, indicator)
                    logger.info(f"✅ 检测到发布成功指示器: {success_element.text}")
                    success_found = True
                    break
                except NoSuchElementException:
                    continue

            if success_found:
                logger.info("🎉 视频发布成功！")
            else:
                logger.info("📤 视频发布请求已发送，等待处理")

            return True

        except Exception as e:
            logger.error(f"发布视频失败: {e}")
            self._take_screenshot("publish_error")
            return False

    def _take_screenshot(self, name):
        """截取错误截图"""
        try:
            screenshot_dir = "screenshots"
            os.makedirs(screenshot_dir, exist_ok=True)

            timestamp = int(time.time())
            screenshot_path = os.path.join(screenshot_dir, f"{name}_{timestamp}.png")

            self.driver.save_screenshot(screenshot_path)
            logger.info(f"截图已保存: {screenshot_path}")

        except Exception as e:
            logger.error(f"保存截图失败: {e}")
    
    def _set_input_field(self, field_name, value, selectors):
        """通用输入字段设置方法 - 优化版本"""
        logger.info(f"设置{field_name}: {value[:50]}...")
        
        for i, selector in enumerate(selectors):
            try:
                # 减少等待时间，快速查找元素
                element = WebDriverWait(self.driver, 1.5).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                
                # 快速滚动到元素
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                time.sleep(0.1)  # 减少等待时间
                
                # 快速获得焦点并清空
                element.click()
                time.sleep(0.05)  # 减少等待时间
                
                # 快速清空和输入
                success = self._fast_input_content(element, value)
                
                if success:
                    logger.info(f"✅ {field_name}设置成功 (选择器 {i+1})")
                    return True
                else:
                    logger.debug(f"选择器 {i+1} 验证失败")
                    
            except Exception as e:
                logger.debug(f"选择器 {i+1} ({selector}) 失败: {e}")
                continue
        
        logger.warning(f"⚠️ {field_name}设置失败")
        return False
    
    def _fast_input_content(self, element, content):
        """快速输入内容并验证 - 优化版本"""
        try:
            # 快速清空 - 使用最有效的方法
            self.driver.execute_script("""
                var element = arguments[0];
                element.value = '';
                element.textContent = '';
                element.innerHTML = '';
            """, element)
            
            # 快速输入 - 优先使用JavaScript方法
            self.driver.execute_script("""
                var element = arguments[0];
                var content = arguments[1];
                element.value = content;
                element.textContent = content;
                
                // 快速触发必要事件
                ['input', 'change'].forEach(function(eventType) {
                    var event = new Event(eventType, { bubbles: true });
                    element.dispatchEvent(event);
                });
            """, element, content)
            
            time.sleep(0.1)  # 最小等待时间
            
            # 快速验证
            current_value = self._get_element_value(element)
            if content[:10] in current_value or len(current_value.strip()) > 0:
                return True
                
        except Exception as e:
            logger.debug(f"JavaScript输入失败: {e}")
        
        try:
            # 备用方法: 直接输入（如果JS失败）
            element.clear()
            element.send_keys(content)
            time.sleep(0.1)
            
            # 验证输入
            current_value = self._get_element_value(element)
            if content[:10] in current_value or len(current_value.strip()) > 0:
                return True
                
        except Exception as e:
            logger.debug(f"直接输入失败: {e}")
        
        return False
    
    def _get_element_value(self, element):
        """获取元素值"""
        try:
            return (element.get_attribute('textContent') or 
                   element.get_attribute('value') or 
                   element.get_attribute('innerText') or '')
        except:
            return ''
    
    def _set_children_content_option(self, children_content):
        """设置儿童内容选项"""
        logger.info(f"设置儿童内容选项: {'是' if children_content else '否'}")
        
        # 定义选择器策略
        if children_content:
            # 选择"是，这是为儿童制作的影片"
            selectors = [
                "//tp-yt-paper-radio-button[@name='VIDEO_MADE_FOR_KIDS_MFK']",
                "//input[@name='VIDEO_MADE_FOR_KIDS_MFK']",
                "//input[@value='VIDEO_MADE_FOR_KIDS_MFK']",
                "//div[contains(text(), '是') and (contains(text(), '儿童') or contains(text(), '兒童'))]//ancestor::*[1]//tp-yt-paper-radio-button",
                "//div[contains(text(), '是') and (contains(text(), '儿童') or contains(text(), '兒童'))]//ancestor::*[1]//input[@type='radio']",
                "(//tp-yt-paper-radio-button)[1]",
                "(//input[@type='radio'])[1]"
            ]
        else:
            # 选择"否，这不是为儿童制作的影片"
            selectors = [
                "//tp-yt-paper-radio-button[@name='VIDEO_MADE_FOR_KIDS_NOT_MFK']",
                "//input[@name='VIDEO_MADE_FOR_KIDS_NOT_MFK']",
                "//input[@value='VIDEO_MADE_FOR_KIDS_NOT_MFK']",
                "//div[contains(text(), '否') and (contains(text(), '儿童') or contains(text(), '兒童'))]//ancestor::*[1]//tp-yt-paper-radio-button",
                "//div[contains(text(), '否') and (contains(text(), '儿童') or contains(text(), '兒童'))]//ancestor::*[1]//input[@type='radio']",
                "(//tp-yt-paper-radio-button)[2]",
                "(//input[@type='radio'])[2]"
            ]
        
        # 快速尝试点击选项
        for i, selector in enumerate(selectors):
            try:
                element = WebDriverWait(self.driver, 1.5).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                
                # 快速滚动到元素
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                time.sleep(0.1)
                
                # 点击元素
                try:
                    element.click()
                except:
                    # 如果普通点击失败，使用JavaScript点击
                    self.driver.execute_script("arguments[0].click();", element)
                
                time.sleep(0.5)
                
                # 验证是否选中
                try:
                    is_checked = (element.get_attribute('checked') == 'true' or 
                                element.get_attribute('aria-checked') == 'true' or
                                'checked' in element.get_attribute('class') or '')
                    
                    if is_checked:
                        logger.info(f"✅ 儿童内容选项设置成功 (选择器 {i+1})")
                        return True
                except:
                    # 如果无法验证，假设成功
                    logger.info(f"✅ 儿童内容选项设置完成 (选择器 {i+1})")
                    return True
                    
            except Exception as e:
                logger.debug(f"选择器 {i+1} ({selector}) 失败: {e}")
                continue
        
        logger.warning("⚠️ 儿童内容选项设置失败")
        return False
    
    def _debug_editable_elements(self, element_type):
        """调试：查找页面上所有可编辑元素"""
        try:
            # 截图保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = f"debug_{element_type}_{timestamp}.png"
            self.driver.save_screenshot(screenshot_path)
            logger.info(f"调试截图已保存: {screenshot_path}")
            
            # 查找所有可编辑元素
            editable_selectors = [
                "//div[@contenteditable='true']",
                "//textarea",
                "//input[@type='text']",
                "//div[@role='textbox']",
                "//ytcp-social-suggestions-textbox"
            ]
            
            logger.info(f"=== 调试 {element_type} 输入框 ===")
            for i, selector in enumerate(editable_selectors):
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    logger.info(f"选择器 {i+1}: {selector} - 找到 {len(elements)} 个元素")
                    
                    for j, element in enumerate(elements[:5]):  # 只显示前5个
                        try:
                            aria_label = element.get_attribute('aria-label') or '无'
                            class_name = element.get_attribute('class') or '无'
                            tag_name = element.tag_name
                            text_content = (element.get_attribute('textContent') or '')[:50]
                            
                            logger.info(f"  元素 {j+1}: {tag_name}, aria-label='{aria_label}', class='{class_name}', text='{text_content}'")
                        except Exception as e:
                            logger.debug(f"  元素 {j+1}: 获取属性失败 - {e}")
                            
                except Exception as e:
                    logger.debug(f"选择器 {selector} 查找失败: {e}")
            
            # 尝试查找页面标题，确认当前页面
            try:
                page_title = self.driver.title
                current_url = self.driver.current_url
                logger.info(f"当前页面: {page_title}")
                logger.info(f"当前URL: {current_url}")
            except:
                pass
                
        except Exception as e:
            logger.error(f"调试过程出错: {e}")

    def close(self):
        """关闭上传器"""
        try:
            if self.driver:
                self.driver.quit()
                logger.info("统一视频上传器已关闭")
        except Exception as e:
            logger.error(f"关闭上传器失败: {e}")