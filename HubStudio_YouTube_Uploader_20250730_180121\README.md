# HubStudio + YouTube 自动化上传工具

专门为 HubStudio 指纹浏览器和 YouTube 平台优化的自动化视频上传工具。

## 🎯 核心特点

- 🚀 **专业优化**：专门针对 HubStudio + YouTube 组合优化
- 🤖 **全自动化**：从启动浏览器到视频发布，完全自动化
- 🎬 **用户友好**：图形界面，用户可自定义标题、描述、儿童内容设置
- 📁 **批量处理**：支持文件夹批量上传
- 🛡️ **错误处理**：完善的重试机制和错误截图
- 📊 **详细日志**：完整的操作记录和状态监控

## 📋 系统要求

- Python 3.7+
- HubStudio 指纹浏览器
- 已登录的 YouTube 账号

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 修复ChromeDriver（如果需要）

```bash
python fix_chromedriver.py
```

### 3. 启动GUI界面

```bash
python run_gui.py
```

### 4. 使用步骤

1. **HubStudio设置**：
   - 启动HubStudio客户端
   - 创建Chrome浏览器环境
   - 在环境中登录YouTube账号

2. **GUI操作**：
   - 程序自动检测HubStudio环境
   - 输入视频标题和描述
   - 选择儿童内容设置
   - 添加视频文件
   - 点击"开始自动上传"

## 🔧 故障排除

### 常见问题

#### ❌ "ChromeDriver版本不匹配"
**解决**：运行 `python fix_chromedriver.py`

#### ❌ "未发现HubStudio环境"
**解决**：在HubStudio中创建Chrome浏览器环境

#### ❌ "连接失败"
**解决**：确保HubStudio客户端正在运行

### 🔍 诊断工具
```bash
# 一键修复常见问题
python fix_upload_issue.py

# 修复ChromeDriver
python fix_chromedriver.py
```

## 📁 项目文件说明

### 核心文件
- `run_gui.py` - 启动GUI界面
- `gui_main.py` - GUI主程序
- `main.py` - 自动化核心逻辑
- `browser_manager.py` - HubStudio浏览器管理
- `video_uploader.py` - YouTube上传功能
- `video_processor.py` - 视频处理和验证

### 配置文件
- `config.ini` - 主配置文件
- `requirements.txt` - Python依赖

### 工具文件
- `fix_chromedriver.py` - ChromeDriver修复工具
- `fix_upload_issue.py` - 一键问题修复工具

### 数据目录
- `videos/` - 视频文件存放目录
- `logs/` - 日志文件目录
- `screenshots/` - 错误截图目录

## ✨ 新功能特性

### 用户友好的GUI界面
- **自定义标题和描述**：用户可以在GUI中直接输入视频标题和描述
- **儿童内容设置**：可选择"是为儿童制作"或"不是为儿童制作"
- **实时预览**：上传前可以预览所有设置
- **批量上传**：支持多个视频使用相同的设置

### 智能化功能
- **自动环境检测**：无需手动配置，自动检测所有可用的HubStudio环境
- **版本自动修复**：内置ChromeDriver版本修复工具
- **错误自动处理**：智能重试机制和详细错误报告

## 许可证

本项目仅供学习和研究使用，请遵守相关平台的使用条款。
