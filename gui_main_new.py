"""
HubStudio + YouTube 自动化上传 - 重构版主入口
采用MVC架构，分离界面展示和业务逻辑
"""

import tkinter as tk
import sys
import os

# 添加gui目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'gui'))

# 直接导入简化版GUI
import importlib.util
import os

# 加载控制器
controller_path = os.path.join(os.path.dirname(__file__), 'gui', 'gui_controller.py')
controller_spec = importlib.util.spec_from_file_location("gui_controller", controller_path)
controller_module = importlib.util.module_from_spec(controller_spec)
controller_spec.loader.exec_module(controller_module)
GUIController = controller_module.GUIController

# 加载简化版视图
view_path = os.path.join(os.path.dirname(__file__), 'gui', 'gui_view_simple.py')
if os.path.exists(view_path):
    view_spec = importlib.util.spec_from_file_location("gui_view_simple", view_path)
    view_module = importlib.util.module_from_spec(view_spec)
    view_spec.loader.exec_module(view_module)
    GUIView = view_module.GUIView
else:
    # 如果简化版不存在，尝试使用重命名后的版本
    view_path = os.path.join(os.path.dirname(__file__), 'gui', 'gui_view.py')
    view_spec = importlib.util.spec_from_file_location("gui_view", view_path)
    view_module = importlib.util.module_from_spec(view_spec)
    view_spec.loader.exec_module(view_module)
    GUIView = view_module.GUIView


class HubStudioApp:
    """主应用程序类 - 协调控制器和视图"""
    
    def __init__(self):
        # 创建主窗口
        self.root = tk.Tk()
        
        # 创建控制器（业务逻辑）
        self.controller = GUIController()
        
        # 创建视图（界面展示）
        self.view = GUIView(self.root, self.controller)
        
        # 初始化完成后自动刷新环境列表
        self.root.after(1000, self._auto_refresh_environments)
    
    def _auto_refresh_environments(self):
        """自动刷新环境列表"""
        api_url = self.controller.get_config('BROWSER', 'api_url')
        api_id = self.controller.get_config('HUBSTUDIO', 'api_id')
        api_secret = self.controller.get_config('HUBSTUDIO', 'api_secret')
        
        if api_id and api_secret:
            self.controller.refresh_environments(api_url, api_id, api_secret)
    
    def run(self):
        """运行应用程序"""
        try:
            # 启动主循环
            self.root.mainloop()
        except KeyboardInterrupt:
            print("应用程序被用户中断")
        except Exception as e:
            print(f"应用程序运行异常: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理资源
            try:
                self.root.quit()
            except:
                pass


def main():
    """主函数"""
    try:
        # 创建并运行应用程序
        app = HubStudioApp()
        app.run()
    except Exception as e:
        print(f"启动应用程序失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()