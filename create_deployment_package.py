#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建部署包脚本
将编译好的exe文件和必要的配置文件打包成完整的部署包
"""

import os
import shutil
import zipfile
from datetime import datetime
from pathlib import Path

def create_deployment_package():
    """创建部署包"""
    
    # 创建部署包目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"HubStudio_YouTube_Uploader_{timestamp}"
    package_dir = Path(package_name)
    
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    
    print(f"📦 创建部署包: {package_name}")
    
    # 复制exe文件
    exe_files = [
        'dist/HubStudio_YouTube_Uploader_GUI.exe',
        'dist/HubStudio_YouTube_Uploader_CLI.exe'
    ]
    
    for exe_file in exe_files:
        if os.path.exists(exe_file):
            shutil.copy2(exe_file, package_dir)
            print(f"✅ 已复制: {os.path.basename(exe_file)}")
        else:
            print(f"⚠️ 文件不存在: {exe_file}")
    
    # 复制配置文件
    config_files = [
        'config.ini',
        '部署使用说明.md',
        'README.md'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            shutil.copy2(config_file, package_dir)
            print(f"✅ 已复制: {config_file}")
    
    # 创建必要的目录
    dirs_to_create = ['logs', 'screenshots', 'videos']
    for dir_name in dirs_to_create:
        (package_dir / dir_name).mkdir()
        # 创建说明文件
        with open(package_dir / dir_name / 'README.txt', 'w', encoding='utf-8') as f:
            if dir_name == 'logs':
                f.write('此目录用于存放程序运行日志文件\n')
            elif dir_name == 'screenshots':
                f.write('此目录用于存放错误截图文件\n')
            elif dir_name == 'videos':
                f.write('此目录用于存放待上传的视频文件\n')
        print(f"✅ 已创建目录: {dir_name}")
    
    # 创建启动脚本
    create_batch_scripts(package_dir)
    
    # 创建压缩包
    zip_filename = f"{package_name}.zip"
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"📦 压缩包已创建: {zip_filename}")
    
    # 显示包信息
    show_package_info(package_dir, zip_filename)
    
    return package_dir, zip_filename

def create_batch_scripts(package_dir):
    """创建批处理启动脚本"""
    
    # GUI启动脚本
    gui_script = '''@echo off
chcp 65001 >nul
echo ========================================
echo HubStudio + YouTube 自动化上传工具
echo GUI版本启动器
echo ========================================
echo.

echo 正在启动GUI界面...
echo 请确保HubStudio客户端正在运行
echo.

start "" "HubStudio_YouTube_Uploader_GUI.exe"

echo GUI程序已启动
echo 如果程序没有显示，请检查是否被杀毒软件拦截
pause
'''
    
    # CLI启动脚本
    cli_script = '''@echo off
chcp 65001 >nul
echo ========================================
echo HubStudio + YouTube 自动化上传工具
echo 命令行版本启动器
echo ========================================
echo.

echo 使用说明:
echo 1. 批量上传默认文件夹: 直接按回车
echo 2. 上传单个文件: 输入完整文件路径
echo 3. 上传指定文件夹: 输入文件夹路径
echo.

set /p input="请输入文件/文件夹路径（直接回车使用默认）: "

if "%input%"=="" (
    echo 使用默认视频文件夹进行批量上传...
    "HubStudio_YouTube_Uploader_CLI.exe"
) else (
    echo 上传指定路径: %input%
    "HubStudio_YouTube_Uploader_CLI.exe" "%input%"
)

echo.
echo 程序执行完成
pause
'''
    
    # 写入脚本文件
    with open(package_dir / '启动GUI版本.bat', 'w', encoding='utf-8') as f:
        f.write(gui_script)
    
    with open(package_dir / '启动命令行版本.bat', 'w', encoding='utf-8') as f:
        f.write(cli_script)
    
    print("✅ 已创建启动脚本")

def show_package_info(package_dir, zip_filename):
    """显示包信息"""
    print("\n" + "="*50)
    print("📋 部署包信息")
    print("="*50)
    
    # 统计文件
    total_files = 0
    total_size = 0
    
    for root, dirs, files in os.walk(package_dir):
        for file in files:
            file_path = os.path.join(root, file)
            file_size = os.path.getsize(file_path)
            total_files += 1
            total_size += file_size
            
            # 显示主要文件信息
            if file.endswith('.exe'):
                size_mb = file_size / (1024 * 1024)
                print(f"  📱 {file}: {size_mb:.1f} MB")
    
    print(f"\n📊 统计信息:")
    print(f"  总文件数: {total_files}")
    print(f"  总大小: {total_size / (1024 * 1024):.1f} MB")
    
    if os.path.exists(zip_filename):
        zip_size = os.path.getsize(zip_filename)
        print(f"  压缩包大小: {zip_size / (1024 * 1024):.1f} MB")
    
    print(f"\n📁 部署包目录: {package_dir}")
    print(f"📦 压缩包文件: {zip_filename}")
    
    print("\n🚀 部署说明:")
    print("1. 将压缩包解压到目标机器")
    print("2. 确保HubStudio客户端正在运行")
    print("3. 双击启动脚本或直接运行exe文件")
    print("4. 首次运行时配置config.ini文件")

def main():
    """主函数"""
    print("📦 HubStudio YouTube上传工具 - 部署包创建脚本")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        'dist/HubStudio_YouTube_Uploader_GUI.exe',
        'dist/HubStudio_YouTube_Uploader_CLI.exe'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"  - {file}")
        print("\n请先运行编译脚本生成exe文件")
        return
    
    # 创建部署包
    package_dir, zip_filename = create_deployment_package()
    
    print(f"\n🎉 部署包创建完成！")
    print(f"📁 目录: {package_dir}")
    print(f"📦 压缩包: {zip_filename}")

if __name__ == "__main__":
    main()
