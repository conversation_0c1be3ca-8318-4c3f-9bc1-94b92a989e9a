2025-07-30 19:10:40 | INFO     | __main__:setup_logging:166 - 📝 日志系统初始化成功
2025-07-30 19:10:40 | INFO     | __main__:setup_logging:167 -    日志级别: INFO
2025-07-30 19:10:40 | INFO     | __main__:setup_logging:168 -    日志文件: C:\Users\<USER>\Desktop\project\dianshang\dist\./logs/automation.log
2025-07-30 19:10:40 | INFO     | __main__:initialize:179 - 🚀 开始初始化HubStudio + YouTube自动化系统
2025-07-30 19:10:40 | INFO     | __main__:initialize:182 - 📋 初始化HubStudio管理器...
2025-07-30 19:10:40 | INFO     | __main__:initialize:195 - 🌐 启动HubStudio浏览器环境...
2025-07-30 19:10:41 | INFO     | browser_manager:start_browser_profile:148 - 🔍 获取可用的浏览器环境...
2025-07-30 19:10:41 | INFO     | browser_manager:get_environment_list:28 - 获取HubStudio环境列表...
2025-07-30 19:10:41 | INFO     | browser_manager:get_environment_list:80 - 获取到 10 个环境
2025-07-30 19:10:41 | INFO     | browser_manager:get_environment_list:93 - 转换后有 10 个可用环境
2025-07-30 19:10:41 | INFO     | browser_manager:start_browser_profile:155 - ✅ 使用第一个可用环境: 新建环境10 (ID: 1285729809)
2025-07-30 19:10:41 | INFO     | browser_manager:start_browser_profile:157 - 🚀 启动浏览器环境: 1285729809
2025-07-30 19:10:53 | INFO     | browser_manager:start_browser_profile:217 - ✅ 浏览器启动成功，调试端口: 63994
2025-07-30 19:10:53 | INFO     | browser_manager:_connect_to_browser:268 - 🔗 连接到浏览器调试端口: 63994
2025-07-30 19:10:53 | INFO     | browser_manager:_connect_to_browser:281 - ✅ 调试端口 63994 可访问
2025-07-30 19:10:53 | INFO     | browser_manager:_connect_to_browser:297 - 🔧 使用HubStudio自带的webdriver: C:\Users\<USER>\AppData\Local\env-kit\Core\chrome_64_133_202505210948\webdriver.exe
2025-07-30 19:10:53 | INFO     | browser_manager:_connect_to_browser:304 - ✅ 使用HubStudio webdriver
2025-07-30 19:10:54 | INFO     | browser_manager:_connect_to_browser:320 - ✅ WebDriver实例创建成功
2025-07-30 19:10:54 | INFO     | browser_manager:_connect_to_browser:329 - 🧪 测试浏览器连接 (尝试 1/3)
2025-07-30 19:10:54 | INFO     | browser_manager:_connect_to_browser:336 - ✅ 浏览器连接测试成功
2025-07-30 19:10:54 | INFO     | browser_manager:_connect_to_browser:337 -    标题: 
2025-07-30 19:10:54 | INFO     | browser_manager:_connect_to_browser:338 -    URL: about:blank
2025-07-30 19:10:54 | INFO     | browser_manager:_connect_to_browser:344 - ✅ JavaScript执行测试成功
2025-07-30 19:10:54 | INFO     | browser_manager:start_browser_profile:226 - 🎉 成功连接到HubStudio浏览器
2025-07-30 19:10:54 | INFO     | __main__:initialize:211 - 🧪 验证浏览器连接...
2025-07-30 19:10:54 | INFO     | __main__:initialize:214 - ✅ 浏览器连接验证成功
2025-07-30 19:10:54 | INFO     | __main__:initialize:223 - 📤 初始化统一视频上传器...
2025-07-30 19:10:54 | INFO     | __main__:initialize:226 - ✅ 视频上传器初始化成功
2025-07-30 19:10:54 | INFO     | __main__:initialize:232 - 🎉 HubStudio + YouTube 自动化系统初始化成功
2025-07-30 19:10:54 | INFO     | __main__:initialize:233 - 📊 系统状态:
2025-07-30 19:10:54 | INFO     | __main__:initialize:234 -    - HubStudio管理器: ✅ 已连接
2025-07-30 19:10:54 | INFO     | __main__:initialize:235 -    - 浏览器环境: ✅ 已启动
2025-07-30 19:10:54 | INFO     | __main__:initialize:236 -    - 视频上传器: ✅ 已就绪
2025-07-30 19:10:54 | WARNING  | __main__:upload_batch_videos:335 - 在文件夹中未找到视频文件: ./videos
2025-07-30 19:10:54 | INFO     | __main__:cleanup:397 - 清理资源...
2025-07-30 19:10:56 | INFO     | video_uploader_unified:close:1510 - 统一视频上传器已关闭
2025-07-30 19:10:56 | INFO     | browser_manager:stop_browser_profile:377 - 关闭WebDriver连接
2025-07-30 19:11:12 | INFO     | browser_manager:stop_browser_profile:382 - 停止浏览器环境: 1285729809
2025-07-30 19:11:14 | INFO     | browser_manager:stop_browser_profile:392 - 浏览器环境已停止
2025-07-30 19:11:14 | INFO     | __main__:cleanup:405 - 资源清理完成
2025-07-30 19:21:39 | INFO     | __main__:setup_logging:181 - 📝 日志系统初始化成功
2025-07-30 19:21:39 | INFO     | __main__:setup_logging:182 -    日志级别: INFO
2025-07-30 19:21:39 | INFO     | __main__:setup_logging:183 -    日志文件: C:\Users\<USER>\Desktop\project\dianshang\dist\./logs/automation.log
2025-07-30 19:21:39 | INFO     | __main__:initialize:194 - 🚀 开始初始化HubStudio + YouTube自动化系统
2025-07-30 19:21:39 | INFO     | __main__:initialize:197 - 📋 初始化HubStudio管理器...
2025-07-30 19:21:40 | INFO     | __main__:initialize:210 - 🌐 启动HubStudio浏览器环境...
2025-07-30 19:21:41 | INFO     | browser_manager:start_browser_profile:148 - 🔍 获取可用的浏览器环境...
2025-07-30 19:21:41 | INFO     | browser_manager:get_environment_list:28 - 获取HubStudio环境列表...
2025-07-30 19:21:41 | INFO     | browser_manager:get_environment_list:80 - 获取到 10 个环境
2025-07-30 19:21:41 | INFO     | browser_manager:get_environment_list:93 - 转换后有 10 个可用环境
2025-07-30 19:21:41 | INFO     | browser_manager:start_browser_profile:155 - ✅ 使用第一个可用环境: 新建环境10 (ID: 1285729809)
2025-07-30 19:21:41 | INFO     | browser_manager:start_browser_profile:157 - 🚀 启动浏览器环境: 1285729809
2025-07-30 19:21:47 | INFO     | browser_manager:start_browser_profile:217 - ✅ 浏览器启动成功，调试端口: 51037
2025-07-30 19:21:47 | INFO     | browser_manager:_connect_to_browser:268 - 🔗 连接到浏览器调试端口: 51037
2025-07-30 19:21:47 | INFO     | browser_manager:_connect_to_browser:281 - ✅ 调试端口 51037 可访问
2025-07-30 19:21:47 | INFO     | browser_manager:_connect_to_browser:297 - 🔧 使用HubStudio自带的webdriver: C:\Users\<USER>\AppData\Local\env-kit\Core\chrome_64_133_202505210948\webdriver.exe
2025-07-30 19:21:47 | INFO     | browser_manager:_connect_to_browser:304 - ✅ 使用HubStudio webdriver
2025-07-30 19:21:48 | INFO     | browser_manager:_connect_to_browser:320 - ✅ WebDriver实例创建成功
2025-07-30 19:21:48 | INFO     | browser_manager:_connect_to_browser:329 - 🧪 测试浏览器连接 (尝试 1/3)
2025-07-30 19:21:48 | INFO     | browser_manager:_connect_to_browser:336 - ✅ 浏览器连接测试成功
2025-07-30 19:21:48 | INFO     | browser_manager:_connect_to_browser:337 -    标题: 
2025-07-30 19:21:48 | INFO     | browser_manager:_connect_to_browser:338 -    URL: about:blank
2025-07-30 19:21:48 | INFO     | browser_manager:_connect_to_browser:344 - ✅ JavaScript执行测试成功
2025-07-30 19:21:48 | INFO     | browser_manager:start_browser_profile:226 - 🎉 成功连接到HubStudio浏览器
2025-07-30 19:21:48 | INFO     | __main__:initialize:226 - 🧪 验证浏览器连接...
2025-07-30 19:21:48 | INFO     | __main__:initialize:229 - ✅ 浏览器连接验证成功
2025-07-30 19:21:48 | INFO     | __main__:initialize:238 - 📤 初始化统一视频上传器...
2025-07-30 19:21:48 | INFO     | __main__:initialize:241 - ✅ 视频上传器初始化成功
2025-07-30 19:21:48 | INFO     | __main__:initialize:247 - 🎉 HubStudio + YouTube 自动化系统初始化成功
2025-07-30 19:21:48 | INFO     | __main__:initialize:248 - 📊 系统状态:
2025-07-30 19:21:48 | INFO     | __main__:initialize:249 -    - HubStudio管理器: ✅ 已连接
2025-07-30 19:21:48 | INFO     | __main__:initialize:250 -    - 浏览器环境: ✅ 已启动
2025-07-30 19:21:48 | INFO     | __main__:initialize:251 -    - 视频上传器: ✅ 已就绪
2025-07-30 19:21:48 | WARNING  | __main__:upload_batch_videos:350 - 在文件夹中未找到视频文件: ./videos
2025-07-30 19:21:48 | INFO     | __main__:cleanup:412 - 清理资源...
2025-07-30 19:21:50 | INFO     | video_uploader_unified:close:1510 - 统一视频上传器已关闭
2025-07-30 19:21:50 | INFO     | browser_manager:stop_browser_profile:377 - 关闭WebDriver连接
2025-07-30 19:22:06 | INFO     | browser_manager:stop_browser_profile:382 - 停止浏览器环境: 1285729809
2025-07-30 19:22:08 | INFO     | browser_manager:stop_browser_profile:392 - 浏览器环境已停止
2025-07-30 19:22:08 | INFO     | __main__:cleanup:420 - 资源清理完成
2025-07-30 19:29:23 | INFO     | __main__:setup_logging:257 - 📝 日志系统初始化成功
2025-07-30 19:29:23 | INFO     | __main__:setup_logging:258 -    日志级别: INFO
2025-07-30 19:29:23 | INFO     | __main__:setup_logging:259 -    日志文件: C:\Users\<USER>\Desktop\project\dianshang\dist\./logs/automation.log
2025-07-30 19:29:23 | INFO     | __main__:__init__:65 - 🎉 HubStudioYouTubeAutomation 初始化成功
2025-07-30 19:29:23 | INFO     | __main__:initialize:281 - 🚀 开始初始化HubStudio + YouTube自动化系统
2025-07-30 19:29:23 | INFO     | __main__:initialize:284 - 📋 初始化HubStudio管理器...
2025-07-30 19:29:24 | INFO     | __main__:initialize:297 - 🌐 启动HubStudio浏览器环境...
2025-07-30 19:29:24 | INFO     | browser_manager:start_browser_profile:148 - 🔍 获取可用的浏览器环境...
2025-07-30 19:29:24 | INFO     | browser_manager:get_environment_list:28 - 获取HubStudio环境列表...
2025-07-30 19:29:25 | INFO     | browser_manager:get_environment_list:80 - 获取到 10 个环境
2025-07-30 19:29:25 | INFO     | browser_manager:get_environment_list:93 - 转换后有 10 个可用环境
2025-07-30 19:29:25 | INFO     | browser_manager:start_browser_profile:155 - ✅ 使用第一个可用环境: 新建环境10 (ID: 1285729809)
2025-07-30 19:29:25 | INFO     | browser_manager:start_browser_profile:157 - 🚀 启动浏览器环境: 1285729809
2025-07-30 19:29:31 | INFO     | browser_manager:start_browser_profile:217 - ✅ 浏览器启动成功，调试端口: 53266
2025-07-30 19:29:31 | INFO     | browser_manager:_connect_to_browser:268 - 🔗 连接到浏览器调试端口: 53266
2025-07-30 19:29:31 | INFO     | browser_manager:_connect_to_browser:281 - ✅ 调试端口 53266 可访问
2025-07-30 19:29:31 | INFO     | browser_manager:_connect_to_browser:297 - 🔧 使用HubStudio自带的webdriver: C:\Users\<USER>\AppData\Local\env-kit\Core\chrome_64_133_202505210948\webdriver.exe
2025-07-30 19:29:31 | INFO     | browser_manager:_connect_to_browser:304 - ✅ 使用HubStudio webdriver
2025-07-30 19:29:32 | INFO     | browser_manager:_connect_to_browser:320 - ✅ WebDriver实例创建成功
2025-07-30 19:29:32 | INFO     | browser_manager:_connect_to_browser:329 - 🧪 测试浏览器连接 (尝试 1/3)
2025-07-30 19:29:32 | INFO     | browser_manager:_connect_to_browser:336 - ✅ 浏览器连接测试成功
2025-07-30 19:29:32 | INFO     | browser_manager:_connect_to_browser:337 -    标题: 
2025-07-30 19:29:32 | INFO     | browser_manager:_connect_to_browser:338 -    URL: about:blank
2025-07-30 19:29:32 | INFO     | browser_manager:_connect_to_browser:344 - ✅ JavaScript执行测试成功
2025-07-30 19:29:32 | INFO     | browser_manager:start_browser_profile:226 - 🎉 成功连接到HubStudio浏览器
2025-07-30 19:29:32 | INFO     | __main__:initialize:313 - 🧪 验证浏览器连接...
2025-07-30 19:29:32 | INFO     | __main__:initialize:316 - ✅ 浏览器连接验证成功
2025-07-30 19:29:32 | INFO     | __main__:initialize:325 - 📤 初始化统一视频上传器...
2025-07-30 19:29:32 | INFO     | __main__:initialize:328 - ✅ 视频上传器初始化成功
2025-07-30 19:29:32 | INFO     | __main__:initialize:334 - 🎉 HubStudio + YouTube 自动化系统初始化成功
2025-07-30 19:29:32 | INFO     | __main__:initialize:335 - 📊 系统状态:
2025-07-30 19:29:32 | INFO     | __main__:initialize:336 -    - HubStudio管理器: ✅ 已连接
2025-07-30 19:29:32 | INFO     | __main__:initialize:337 -    - 浏览器环境: ✅ 已启动
2025-07-30 19:29:32 | INFO     | __main__:initialize:338 -    - 视频上传器: ✅ 已就绪
2025-07-30 19:29:32 | WARNING  | __main__:upload_batch_videos:437 - 在文件夹中未找到视频文件: ./videos
2025-07-30 19:29:32 | INFO     | __main__:cleanup:499 - 清理资源...
2025-07-30 19:29:34 | INFO     | video_uploader_unified:close:1510 - 统一视频上传器已关闭
2025-07-30 19:29:34 | INFO     | browser_manager:stop_browser_profile:377 - 关闭WebDriver连接
2025-07-30 19:29:50 | INFO     | browser_manager:stop_browser_profile:382 - 停止浏览器环境: 1285729809
2025-07-30 19:29:53 | INFO     | browser_manager:stop_browser_profile:392 - 浏览器环境已停止
2025-07-30 19:29:53 | INFO     | __main__:cleanup:507 - 资源清理完成
2025-07-30 19:30:42 | INFO     | main:setup_logging:257 - 📝 日志系统初始化成功
2025-07-30 19:30:42 | INFO     | main:setup_logging:258 -    日志级别: INFO
2025-07-30 19:30:42 | INFO     | main:setup_logging:259 -    日志文件: C:\Users\<USER>\Desktop\project\dianshang\dist\./logs/automation.log
2025-07-30 19:30:42 | INFO     | main:__init__:65 - 🎉 HubStudioYouTubeAutomation 初始化成功
2025-07-30 19:30:42 | INFO     | main:initialize:281 - 🚀 开始初始化HubStudio + YouTube自动化系统
2025-07-30 19:30:42 | INFO     | main:initialize:284 - 📋 初始化HubStudio管理器...
2025-07-30 19:30:43 | INFO     | main:initialize:297 - 🌐 启动HubStudio浏览器环境...
2025-07-30 19:30:43 | INFO     | browser_manager:start_browser_profile:157 - 🚀 启动浏览器环境: 1281247135
2025-07-30 19:30:49 | INFO     | browser_manager:start_browser_profile:217 - ✅ 浏览器启动成功，调试端口: 53787
2025-07-30 19:30:49 | INFO     | browser_manager:_connect_to_browser:268 - 🔗 连接到浏览器调试端口: 53787
2025-07-30 19:30:49 | INFO     | browser_manager:_connect_to_browser:281 - ✅ 调试端口 53787 可访问
2025-07-30 19:30:49 | INFO     | browser_manager:_connect_to_browser:297 - 🔧 使用HubStudio自带的webdriver: C:\Users\<USER>\AppData\Local\env-kit\Core\chrome_64_133_202505210948\webdriver.exe
2025-07-30 19:30:49 | INFO     | browser_manager:_connect_to_browser:304 - ✅ 使用HubStudio webdriver
2025-07-30 19:30:50 | INFO     | browser_manager:_connect_to_browser:320 - ✅ WebDriver实例创建成功
2025-07-30 19:30:50 | INFO     | browser_manager:_connect_to_browser:329 - 🧪 测试浏览器连接 (尝试 1/3)
2025-07-30 19:30:50 | INFO     | browser_manager:_connect_to_browser:336 - ✅ 浏览器连接测试成功
2025-07-30 19:30:50 | INFO     | browser_manager:_connect_to_browser:337 -    标题: 
2025-07-30 19:30:50 | INFO     | browser_manager:_connect_to_browser:338 -    URL: about:blank
2025-07-30 19:30:50 | INFO     | browser_manager:_connect_to_browser:344 - ✅ JavaScript执行测试成功
2025-07-30 19:30:50 | INFO     | browser_manager:start_browser_profile:226 - 🎉 成功连接到HubStudio浏览器
2025-07-30 19:30:50 | INFO     | main:initialize:313 - 🧪 验证浏览器连接...
2025-07-30 19:30:50 | INFO     | main:initialize:316 - ✅ 浏览器连接验证成功
2025-07-30 19:30:50 | INFO     | main:initialize:325 - 📤 初始化统一视频上传器...
2025-07-30 19:30:50 | INFO     | main:initialize:328 - ✅ 视频上传器初始化成功
2025-07-30 19:30:50 | INFO     | main:initialize:334 - 🎉 HubStudio + YouTube 自动化系统初始化成功
2025-07-30 19:30:50 | INFO     | main:initialize:335 - 📊 系统状态:
2025-07-30 19:30:50 | INFO     | main:initialize:336 -    - HubStudio管理器: ✅ 已连接
2025-07-30 19:30:50 | INFO     | main:initialize:337 -    - 浏览器环境: ✅ 已启动
2025-07-30 19:30:50 | INFO     | main:initialize:338 -    - 视频上传器: ✅ 已就绪
2025-07-30 19:30:50 | INFO     | main:upload_single_video:380 - 📁 视频文件: ea293defa554c31911c1daca3b82c6a5.mp4 (27.1 MB)
2025-07-30 19:30:50 | INFO     | main:upload_single_video:390 - 🚀 开始上传视频: C:/Users/<USER>/Documents/WeChat Files/wxid_jutff6al2uat22/FileStorage/Video/2025-07/ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:30:50 | INFO     | main:upload_single_video:391 - 📝 标题: 4
2025-07-30 19:30:50 | INFO     | main:upload_single_video:392 - 📄 描述: 通过HubStudio自动化脚本上传到YouTube的视频
2025-07-30 19:30:50 | INFO     | main:upload_single_video:393 - 🏷️ 标签: 自动化,HubStudio,YouTube,视频上传
2025-07-30 19:30:50 | INFO     | main:upload_single_video:394 - 👶 儿童内容: 否
2025-07-30 19:30:50 | INFO     | main:upload_single_video:397 - 🎬 开始执行上传流程...
2025-07-30 19:30:50 | INFO     | video_uploader_unified:upload_video:321 - 🎬 开始上传视频: ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:30:50 | INFO     | video_uploader_unified:validate_video_file:119 - 视频文件验证通过: ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:30:50 | INFO     | video_uploader_unified:upload_video:343 - 🌐 步骤1: 导航到YouTube Studio
2025-07-30 19:30:50 | INFO     | video_uploader_unified:_navigate_to_studio:402 - 导航到 YouTube Studio
2025-07-30 19:31:00 | INFO     | video_uploader_unified:_navigate_to_studio:432 - ✅ 成功导航到 YouTube Studio
2025-07-30 19:31:01 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\navigate_to_studio_1753875060.png
2025-07-30 19:31:01 | INFO     | video_uploader_unified:upload_video:349 - 🔘 步骤2: 点击上传按钮
2025-07-30 19:31:01 | INFO     | video_uploader_unified:_click_upload_button:449 - 查找并点击上传按钮
2025-07-30 19:31:04 | INFO     | video_uploader_unified:_click_upload_button:463 - 尝试直接导航到: https://studio.youtube.com/channel/upload
2025-07-30 19:31:10 | INFO     | video_uploader_unified:_click_upload_button:463 - 尝试直接导航到: https://www.youtube.com/upload
2025-07-30 19:31:16 | INFO     | video_uploader_unified:_click_upload_button:470 - ✅ 成功直接导航到上传页面
2025-07-30 19:31:16 | INFO     | video_uploader_unified:upload_video:355 - 📁 步骤3: 上传视频文件
2025-07-30 19:31:16 | INFO     | video_uploader_unified:_upload_file:643 - 🚀 开始上传文件: C:/Users/<USER>/Documents/WeChat Files/wxid_jutff6al2uat22/FileStorage/Video/2025-07/ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:31:16 | INFO     | video_uploader_unified:_upload_file:651 - ✅ 文件路径: C:\Users\<USER>\Documents\WeChat Files\wxid_jutff6al2uat22\FileStorage\Video\2025-07\ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:31:19 | INFO     | video_uploader_unified:_upload_file:657 - 🔍 查找文件输入元素...
2025-07-30 19:31:19 | INFO     | video_uploader_unified:_upload_file:663 - 找到 1 个文件输入元素
2025-07-30 19:31:19 | INFO     | video_uploader_unified:_upload_file:668 - 尝试文件输入 1
2025-07-30 19:31:19 | INFO     | video_uploader_unified:_upload_file:672 - ✅ 文件已发送到输入 1
2025-07-30 19:31:21 | INFO     | video_uploader_unified:_upload_file:686 - 🎉 上传已开始！
2025-07-30 19:31:21 | INFO     | video_uploader_unified:upload_video:361 - 📝 步骤4: 填写视频详细信息
2025-07-30 19:31:21 | INFO     | video_uploader_unified:_fill_details:745 - 填写视频详细资讯
2025-07-30 19:31:23 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\details_page_start_1753875082.png
2025-07-30 19:31:23 | INFO     | video_uploader_unified:_fill_details:755 - 设置标题: 4
2025-07-30 19:31:23 | INFO     | video_uploader_unified:_set_input_field:1293 - 设置title: 4...
2025-07-30 19:31:29 | INFO     | video_uploader_unified:_set_input_field:1314 - ✅ title设置成功 (选择器 4)
2025-07-30 19:31:29 | INFO     | video_uploader_unified:_set_input_field:1293 - 设置描述: 通过HubStudio自动化脚本上传到YouTube的视频...
2025-07-30 19:31:30 | INFO     | video_uploader_unified:_set_input_field:1314 - ✅ 描述设置成功 (选择器 1)
2025-07-30 19:31:30 | INFO     | video_uploader_unified:_fill_details:793 - 设置儿童内容选项: 否
2025-07-30 19:31:33 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\before_kids_content_1753875093.png
2025-07-30 19:31:33 | INFO     | video_uploader_unified:_set_children_content_option:1388 - 设置儿童内容选项: 否
2025-07-30 19:31:34 | INFO     | video_uploader_unified:_set_children_content_option:1441 - ✅ 儿童内容选项设置成功 (选择器 1)
2025-07-30 19:31:34 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\after_kids_content_1753875094.png
2025-07-30 19:31:34 | INFO     | video_uploader_unified:_fill_details:817 - 设置标签: 自动化,HubStudio,YouTube,视频上传
2025-07-30 19:31:34 | INFO     | video_uploader_unified:_set_tags:889 - 设置标签: 自动化,HubStudio,YouTube,视频上传
2025-07-30 19:31:56 | WARNING  | video_uploader_unified:_set_tags:944 - ⚠️ 添加标签失败 '自动化': Message: element click intercepted: Element <input id="text-input" autocomplete="off" class="text-input style-scope ytcp-chip-bar" aria-haspopup="false" aria-autocomplete="list" placeholder="篩選器"> is not clickable at point (564, 207). Other element would receive the click: <ytcp-ve class="style-scope ytcp-uploads-dialog" role="none">...</ytcp-ve>
  (Session info: chrome=133.0.6943.80); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x00007FF610B87EB5+84709]
	(No symbol) [0x00007FF610AE4200]
	(No symbol) [0x00007FF610978F4A]
	(No symbol) [0x00007FF6109D70A9]
	(No symbol) [0x00007FF6109D4A72]
	(No symbol) [0x00007FF6109D1B21]
	(No symbol) [0x00007FF6109D0A31]
	(No symbol) [0x00007FF6109C2224]
	(No symbol) [0x00007FF6109F71EA]
	(No symbol) [0x00007FF6109C1AD6]
	(No symbol) [0x00007FF6109F7400]
	(No symbol) [0x00007FF610A1F693]
	(No symbol) [0x00007FF6109F6FC3]
	(No symbol) [0x00007FF6109BFEFE]
	(No symbol) [0x00007FF6109C1183]
	GetMachineCode [0x00007FF610EB048D+2461117]
	GetMachineCode [0x00007FF610F03B03+2802739]
	GetMachineCode [0x00007FF610EF953D+2760301]
	GetMachineCode [0x00007FF610C5ED0A+30266]
	(No symbol) [0x00007FF610AEEC4F]
	(No symbol) [0x00007FF610AEB214]
	(No symbol) [0x00007FF610AEB3B6]
	(No symbol) [0x00007FF610ADA1E9]
	BaseThreadInitThunk [0x00007FFFE630E8D7+23]
	RtlUserThreadStart [0x00007FFFE80BC34C+44]

2025-07-30 19:31:58 | WARNING  | video_uploader_unified:_set_tags:944 - ⚠️ 添加标签失败 'HubStudio': Message: element click intercepted: Element <input id="text-input" autocomplete="off" class="text-input style-scope ytcp-chip-bar" aria-haspopup="false" aria-autocomplete="list" placeholder="篩選器"> is not clickable at point (564, 207). Other element would receive the click: <ytcp-ve class="style-scope ytcp-uploads-dialog" role="none">...</ytcp-ve>
  (Session info: chrome=133.0.6943.80); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x00007FF610B87EB5+84709]
	(No symbol) [0x00007FF610AE4200]
	(No symbol) [0x00007FF610978F4A]
	(No symbol) [0x00007FF6109D70A9]
	(No symbol) [0x00007FF6109D4A72]
	(No symbol) [0x00007FF6109D1B21]
	(No symbol) [0x00007FF6109D0A31]
	(No symbol) [0x00007FF6109C2224]
	(No symbol) [0x00007FF6109F71EA]
	(No symbol) [0x00007FF6109C1AD6]
	(No symbol) [0x00007FF6109F7400]
	(No symbol) [0x00007FF610A1F693]
	(No symbol) [0x00007FF6109F6FC3]
	(No symbol) [0x00007FF6109BFEFE]
	(No symbol) [0x00007FF6109C1183]
	GetMachineCode [0x00007FF610EB048D+2461117]
	GetMachineCode [0x00007FF610F03B03+2802739]
	GetMachineCode [0x00007FF610EF953D+2760301]
	GetMachineCode [0x00007FF610C5ED0A+30266]
	(No symbol) [0x00007FF610AEEC4F]
	(No symbol) [0x00007FF610AEB214]
	(No symbol) [0x00007FF610AEB3B6]
	(No symbol) [0x00007FF610ADA1E9]
	BaseThreadInitThunk [0x00007FFFE630E8D7+23]
	RtlUserThreadStart [0x00007FFFE80BC34C+44]

2025-07-30 19:31:59 | WARNING  | video_uploader_unified:_set_tags:944 - ⚠️ 添加标签失败 'YouTube': Message: element click intercepted: Element <input id="text-input" autocomplete="off" class="text-input style-scope ytcp-chip-bar" aria-haspopup="false" aria-autocomplete="list" placeholder="篩選器"> is not clickable at point (564, 207). Other element would receive the click: <ytcp-ve class="style-scope ytcp-uploads-dialog" role="none">...</ytcp-ve>
  (Session info: chrome=133.0.6943.80); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x00007FF610B87EB5+84709]
	(No symbol) [0x00007FF610AE4200]
	(No symbol) [0x00007FF610978F4A]
	(No symbol) [0x00007FF6109D70A9]
	(No symbol) [0x00007FF6109D4A72]
	(No symbol) [0x00007FF6109D1B21]
	(No symbol) [0x00007FF6109D0A31]
	(No symbol) [0x00007FF6109C2224]
	(No symbol) [0x00007FF6109F71EA]
	(No symbol) [0x00007FF6109C1AD6]
	(No symbol) [0x00007FF6109F7400]
	(No symbol) [0x00007FF610A1F693]
	(No symbol) [0x00007FF6109F6FC3]
	(No symbol) [0x00007FF6109BFEFE]
	(No symbol) [0x00007FF6109C1183]
	GetMachineCode [0x00007FF610EB048D+2461117]
	GetMachineCode [0x00007FF610F03B03+2802739]
	GetMachineCode [0x00007FF610EF953D+2760301]
	GetMachineCode [0x00007FF610C5ED0A+30266]
	(No symbol) [0x00007FF610AEEC4F]
	(No symbol) [0x00007FF610AEB214]
	(No symbol) [0x00007FF610AEB3B6]
	(No symbol) [0x00007FF610ADA1E9]
	BaseThreadInitThunk [0x00007FFFE630E8D7+23]
	RtlUserThreadStart [0x00007FFFE80BC34C+44]

2025-07-30 19:32:00 | WARNING  | video_uploader_unified:_set_tags:944 - ⚠️ 添加标签失败 '视频上传': Message: element click intercepted: Element <input id="text-input" autocomplete="off" class="text-input style-scope ytcp-chip-bar" aria-haspopup="false" aria-autocomplete="list" placeholder="篩選器"> is not clickable at point (564, 207). Other element would receive the click: <ytcp-ve class="style-scope ytcp-uploads-dialog" role="none">...</ytcp-ve>
  (Session info: chrome=133.0.6943.80); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x00007FF610B87EB5+84709]
	(No symbol) [0x00007FF610AE4200]
	(No symbol) [0x00007FF610978F4A]
	(No symbol) [0x00007FF6109D70A9]
	(No symbol) [0x00007FF6109D4A72]
	(No symbol) [0x00007FF6109D1B21]
	(No symbol) [0x00007FF6109D0A31]
	(No symbol) [0x00007FF6109C2224]
	(No symbol) [0x00007FF6109F71EA]
	(No symbol) [0x00007FF6109C1AD6]
	(No symbol) [0x00007FF6109F7400]
	(No symbol) [0x00007FF610A1F693]
	(No symbol) [0x00007FF6109F6FC3]
	(No symbol) [0x00007FF6109BFEFE]
	(No symbol) [0x00007FF6109C1183]
	GetMachineCode [0x00007FF610EB048D+2461117]
	GetMachineCode [0x00007FF610F03B03+2802739]
	GetMachineCode [0x00007FF610EF953D+2760301]
	GetMachineCode [0x00007FF610C5ED0A+30266]
	(No symbol) [0x00007FF610AEEC4F]
	(No symbol) [0x00007FF610AEB214]
	(No symbol) [0x00007FF610AEB3B6]
	(No symbol) [0x00007FF610ADA1E9]
	BaseThreadInitThunk [0x00007FFFE630E8D7+23]
	RtlUserThreadStart [0x00007FFFE80BC34C+44]

2025-07-30 19:32:00 | INFO     | video_uploader_unified:_set_tags:947 - ✅ 标签设置完成
2025-07-30 19:32:00 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\details_filled_1753875120.png
2025-07-30 19:32:00 | INFO     | video_uploader_unified:_fill_details:824 - 查找下一步按钮...
2025-07-30 19:32:03 | INFO     | video_uploader_unified:_fill_details:866 - ✅ 成功点击下一步按钮
2025-07-30 19:32:06 | INFO     | video_uploader_unified:upload_video:367 - ⏭️ 步骤5: 跳过影片元素页面
2025-07-30 19:32:06 | INFO     | video_uploader_unified:_skip_video_elements:957 - 处理影片元素页面
2025-07-30 19:32:11 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\video_elements_page_1753875131.png
2025-07-30 19:32:15 | INFO     | video_uploader_unified:_skip_video_elements:1009 - ✅ 成功跳过影片元素页面
2025-07-30 19:32:18 | INFO     | video_uploader_unified:upload_video:373 - ✅ 步骤6: 跳过检查项目页面
2025-07-30 19:32:18 | INFO     | video_uploader_unified:_skip_checks:1033 - 处理检查项目页面
2025-07-30 19:32:23 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\checks_page_1753875143.png
2025-07-30 19:32:26 | INFO     | video_uploader_unified:_skip_checks:1085 - ✅ 成功跳过检查项目页面
2025-07-30 19:32:29 | INFO     | video_uploader_unified:upload_video:379 - 🚀 步骤7: 发布视频
2025-07-30 19:32:29 | INFO     | video_uploader_unified:_publish_video:1109 - 处理瀏覽權限和发布视频
2025-07-30 19:32:34 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\publish_page_1753875154.png
2025-07-30 19:32:36 | INFO     | video_uploader_unified:_publish_video:1123 - 设置瀏覽權限为公开
2025-07-30 19:32:37 | INFO     | video_uploader_unified:_publish_video:1156 - ✅ 设置瀏覽權限为公开
2025-07-30 19:32:38 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\visibility_set_public_1753875158.png
2025-07-30 19:32:47 | INFO     | video_uploader_unified:_publish_video:1222 - ✅ 成功点击发布按钮
2025-07-30 19:32:47 | INFO     | video_uploader_unified:_publish_video:1236 - 等待发布完成...
2025-07-30 19:32:57 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\after_publish_1753875177.png
2025-07-30 19:32:57 | INFO     | video_uploader_unified:_publish_video:1258 - ✅ 检测到发布成功指示器: 
2025-07-30 19:32:57 | INFO     | video_uploader_unified:_publish_video:1265 - 🎉 视频发布成功！
2025-07-30 19:32:57 | INFO     | video_uploader_unified:_verify_upload_success:237 - 🔍 验证上传成功状态...
2025-07-30 19:33:01 | INFO     | video_uploader_unified:_verify_upload_success:267 - ✅ 发现成功指示器: 已發布
2025-07-30 19:33:01 | INFO     | video_uploader_unified:_verify_upload_success:272 - 🎉 上传成功验证通过
2025-07-30 19:33:01 | INFO     | video_uploader_unified:upload_video:389 - 🎉 视频上传流程完成！
2025-07-30 19:33:01 | INFO     | main:upload_single_video:401 - 🎉 YouTube视频上传成功: ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:33:01 | INFO     | main:upload_single_video:402 - ✅ 上传完成，视频正在处理中
2025-07-30 19:33:01 | INFO     | main:cleanup:499 - 清理资源...
2025-07-30 19:33:03 | INFO     | video_uploader_unified:close:1510 - 统一视频上传器已关闭
2025-07-30 19:33:03 | INFO     | browser_manager:stop_browser_profile:377 - 关闭WebDriver连接
2025-07-30 19:33:19 | INFO     | browser_manager:stop_browser_profile:382 - 停止浏览器环境: 1281247135
2025-07-30 19:33:21 | INFO     | browser_manager:stop_browser_profile:392 - 浏览器环境已停止
2025-07-30 19:33:21 | INFO     | main:cleanup:507 - 资源清理完成
2025-07-30 19:33:31 | INFO     | main:setup_logging:257 - 📝 日志系统初始化成功
2025-07-30 19:33:31 | INFO     | main:setup_logging:258 -    日志级别: INFO
2025-07-30 19:33:31 | INFO     | main:setup_logging:259 -    日志文件: C:\Users\<USER>\Desktop\project\dianshang\dist\./logs/automation.log
2025-07-30 19:33:31 | INFO     | main:__init__:65 - 🎉 HubStudioYouTubeAutomation 初始化成功
2025-07-30 19:33:31 | INFO     | main:initialize:281 - 🚀 开始初始化HubStudio + YouTube自动化系统
2025-07-30 19:33:31 | INFO     | main:initialize:284 - 📋 初始化HubStudio管理器...
2025-07-30 19:33:32 | INFO     | main:initialize:297 - 🌐 启动HubStudio浏览器环境...
2025-07-30 19:33:32 | INFO     | browser_manager:start_browser_profile:157 - 🚀 启动浏览器环境: 1285729692
2025-07-30 19:33:38 | INFO     | browser_manager:start_browser_profile:217 - ✅ 浏览器启动成功，调试端口: 54748
2025-07-30 19:33:38 | INFO     | browser_manager:_connect_to_browser:268 - 🔗 连接到浏览器调试端口: 54748
2025-07-30 19:33:38 | INFO     | browser_manager:_connect_to_browser:281 - ✅ 调试端口 54748 可访问
2025-07-30 19:33:38 | INFO     | browser_manager:_connect_to_browser:297 - 🔧 使用HubStudio自带的webdriver: C:\Users\<USER>\AppData\Local\env-kit\Core\chrome_64_133_202505210948\webdriver.exe
2025-07-30 19:33:38 | INFO     | browser_manager:_connect_to_browser:304 - ✅ 使用HubStudio webdriver
2025-07-30 19:33:38 | INFO     | browser_manager:_connect_to_browser:320 - ✅ WebDriver实例创建成功
2025-07-30 19:33:38 | INFO     | browser_manager:_connect_to_browser:329 - 🧪 测试浏览器连接 (尝试 1/3)
2025-07-30 19:33:39 | INFO     | browser_manager:_connect_to_browser:336 - ✅ 浏览器连接测试成功
2025-07-30 19:33:39 | INFO     | browser_manager:_connect_to_browser:337 -    标题: 
2025-07-30 19:33:39 | INFO     | browser_manager:_connect_to_browser:338 -    URL: about:blank
2025-07-30 19:33:39 | INFO     | browser_manager:_connect_to_browser:344 - ✅ JavaScript执行测试成功
2025-07-30 19:33:39 | INFO     | browser_manager:start_browser_profile:226 - 🎉 成功连接到HubStudio浏览器
2025-07-30 19:33:39 | INFO     | main:initialize:313 - 🧪 验证浏览器连接...
2025-07-30 19:33:39 | INFO     | main:initialize:316 - ✅ 浏览器连接验证成功
2025-07-30 19:33:39 | INFO     | main:initialize:325 - 📤 初始化统一视频上传器...
2025-07-30 19:33:39 | INFO     | main:initialize:328 - ✅ 视频上传器初始化成功
2025-07-30 19:33:39 | INFO     | main:initialize:334 - 🎉 HubStudio + YouTube 自动化系统初始化成功
2025-07-30 19:33:39 | INFO     | main:initialize:335 - 📊 系统状态:
2025-07-30 19:33:39 | INFO     | main:initialize:336 -    - HubStudio管理器: ✅ 已连接
2025-07-30 19:33:39 | INFO     | main:initialize:337 -    - 浏览器环境: ✅ 已启动
2025-07-30 19:33:39 | INFO     | main:initialize:338 -    - 视频上传器: ✅ 已就绪
2025-07-30 19:33:39 | INFO     | main:upload_single_video:380 - 📁 视频文件: ea293defa554c31911c1daca3b82c6a5.mp4 (27.1 MB)
2025-07-30 19:33:39 | INFO     | main:upload_single_video:390 - 🚀 开始上传视频: C:/Users/<USER>/Documents/WeChat Files/wxid_jutff6al2uat22/FileStorage/Video/2025-07/ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:33:39 | INFO     | main:upload_single_video:391 - 📝 标题: 4
2025-07-30 19:33:39 | INFO     | main:upload_single_video:392 - 📄 描述: 通过HubStudio自动化脚本上传到YouTube的视频
2025-07-30 19:33:39 | INFO     | main:upload_single_video:393 - 🏷️ 标签: 自动化,HubStudio,YouTube,视频上传
2025-07-30 19:33:39 | INFO     | main:upload_single_video:394 - 👶 儿童内容: 否
2025-07-30 19:33:39 | INFO     | main:upload_single_video:397 - 🎬 开始执行上传流程...
2025-07-30 19:33:39 | INFO     | video_uploader_unified:upload_video:321 - 🎬 开始上传视频: ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:33:39 | INFO     | video_uploader_unified:validate_video_file:119 - 视频文件验证通过: ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:33:39 | INFO     | video_uploader_unified:upload_video:343 - 🌐 步骤1: 导航到YouTube Studio
2025-07-30 19:33:39 | INFO     | video_uploader_unified:_navigate_to_studio:402 - 导航到 YouTube Studio
2025-07-30 19:33:46 | ERROR    | video_uploader_unified:_navigate_to_studio:414 - 需要先登录 Google 账号
2025-07-30 19:33:46 | INFO     | video_uploader_unified:_navigate_to_studio:415 - 请在浏览器中手动登录 YouTube，然后重试
2025-07-30 19:33:46 | ERROR    | video_uploader_unified:upload_video:345 - ❌ 导航到YouTube Studio失败
2025-07-30 19:33:46 | ERROR    | main:upload_single_video:404 - ❌ YouTube视频上传失败: ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:33:46 | INFO     | video_uploader_unified:_take_screenshot:1286 - 截图已保存: screenshots\error_1753875226_1753875226.png
2025-07-30 19:33:46 | INFO     | main:upload_single_video:411 - 📸 错误截图已保存: error_1753875226.png
2025-07-30 19:33:46 | INFO     | main:cleanup:499 - 清理资源...
2025-07-30 19:33:48 | INFO     | video_uploader_unified:close:1510 - 统一视频上传器已关闭
2025-07-30 19:33:48 | INFO     | browser_manager:stop_browser_profile:377 - 关闭WebDriver连接
2025-07-30 19:34:05 | INFO     | browser_manager:stop_browser_profile:382 - 停止浏览器环境: 1285729692
2025-07-30 19:34:07 | INFO     | browser_manager:stop_browser_profile:392 - 浏览器环境已停止
2025-07-30 19:34:07 | INFO     | main:cleanup:507 - 资源清理完成
2025-07-30 19:34:17 | INFO     | main:setup_logging:257 - 📝 日志系统初始化成功
2025-07-30 19:34:17 | INFO     | main:setup_logging:258 -    日志级别: INFO
2025-07-30 19:34:17 | INFO     | main:setup_logging:259 -    日志文件: C:\Users\<USER>\Desktop\project\dianshang\dist\./logs/automation.log
2025-07-30 19:34:17 | INFO     | main:__init__:65 - 🎉 HubStudioYouTubeAutomation 初始化成功
2025-07-30 19:34:17 | INFO     | main:initialize:281 - 🚀 开始初始化HubStudio + YouTube自动化系统
2025-07-30 19:34:17 | INFO     | main:initialize:284 - 📋 初始化HubStudio管理器...
2025-07-30 19:34:17 | INFO     | main:initialize:297 - 🌐 启动HubStudio浏览器环境...
2025-07-30 19:34:18 | INFO     | browser_manager:start_browser_profile:157 - 🚀 启动浏览器环境: 1285729718
2025-07-30 19:34:23 | INFO     | browser_manager:start_browser_profile:217 - ✅ 浏览器启动成功，调试端口: 55103
2025-07-30 19:34:23 | INFO     | browser_manager:_connect_to_browser:268 - 🔗 连接到浏览器调试端口: 55103
2025-07-30 19:34:23 | INFO     | browser_manager:_connect_to_browser:281 - ✅ 调试端口 55103 可访问
2025-07-30 19:34:23 | INFO     | browser_manager:_connect_to_browser:297 - 🔧 使用HubStudio自带的webdriver: C:\Users\<USER>\AppData\Local\env-kit\Core\chrome_64_133_202505210948\webdriver.exe
2025-07-30 19:34:23 | INFO     | browser_manager:_connect_to_browser:304 - ✅ 使用HubStudio webdriver
2025-07-30 19:34:24 | INFO     | browser_manager:_connect_to_browser:320 - ✅ WebDriver实例创建成功
2025-07-30 19:34:24 | INFO     | browser_manager:_connect_to_browser:329 - 🧪 测试浏览器连接 (尝试 1/3)
2025-07-30 19:34:24 | INFO     | browser_manager:_connect_to_browser:336 - ✅ 浏览器连接测试成功
2025-07-30 19:34:24 | INFO     | browser_manager:_connect_to_browser:337 -    标题: 
2025-07-30 19:34:24 | INFO     | browser_manager:_connect_to_browser:338 -    URL: about:blank
2025-07-30 19:34:24 | INFO     | browser_manager:_connect_to_browser:344 - ✅ JavaScript执行测试成功
2025-07-30 19:34:24 | INFO     | browser_manager:start_browser_profile:226 - 🎉 成功连接到HubStudio浏览器
2025-07-30 19:34:24 | INFO     | main:initialize:313 - 🧪 验证浏览器连接...
2025-07-30 19:34:24 | INFO     | main:initialize:316 - ✅ 浏览器连接验证成功
2025-07-30 19:34:24 | INFO     | main:initialize:325 - 📤 初始化统一视频上传器...
2025-07-30 19:34:24 | INFO     | main:initialize:328 - ✅ 视频上传器初始化成功
2025-07-30 19:34:24 | INFO     | main:initialize:334 - 🎉 HubStudio + YouTube 自动化系统初始化成功
2025-07-30 19:34:24 | INFO     | main:initialize:335 - 📊 系统状态:
2025-07-30 19:34:24 | INFO     | main:initialize:336 -    - HubStudio管理器: ✅ 已连接
2025-07-30 19:34:24 | INFO     | main:initialize:337 -    - 浏览器环境: ✅ 已启动
2025-07-30 19:34:24 | INFO     | main:initialize:338 -    - 视频上传器: ✅ 已就绪
2025-07-30 19:34:24 | INFO     | main:upload_single_video:380 - 📁 视频文件: ea293defa554c31911c1daca3b82c6a5.mp4 (27.1 MB)
2025-07-30 19:34:24 | INFO     | main:upload_single_video:390 - 🚀 开始上传视频: C:/Users/<USER>/Documents/WeChat Files/wxid_jutff6al2uat22/FileStorage/Video/2025-07/ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:34:24 | INFO     | main:upload_single_video:391 - 📝 标题: 4
2025-07-30 19:34:24 | INFO     | main:upload_single_video:392 - 📄 描述: 通过HubStudio自动化脚本上传到YouTube的视频
2025-07-30 19:34:24 | INFO     | main:upload_single_video:393 - 🏷️ 标签: 自动化,HubStudio,YouTube,视频上传
2025-07-30 19:34:24 | INFO     | main:upload_single_video:394 - 👶 儿童内容: 否
2025-07-30 19:34:24 | INFO     | main:upload_single_video:397 - 🎬 开始执行上传流程...
2025-07-30 19:34:24 | INFO     | video_uploader_unified:upload_video:321 - 🎬 开始上传视频: ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:34:24 | INFO     | video_uploader_unified:validate_video_file:119 - 视频文件验证通过: ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:34:24 | INFO     | video_uploader_unified:upload_video:343 - 🌐 步骤1: 导航到YouTube Studio
2025-07-30 19:34:24 | INFO     | video_uploader_unified:_navigate_to_studio:402 - 导航到 YouTube Studio
2025-07-30 19:34:32 | ERROR    | video_uploader_unified:_navigate_to_studio:442 - 导航到 YouTube Studio 失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=133.0.6943.80); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x00007FF610B87EB5+84709]
	(No symbol) [0x00007FF610AE4200]
	(No symbol) [0x00007FF610978F4A]
	(No symbol) [0x00007FF610964D45]
	(No symbol) [0x00007FF6109896DA]
	(No symbol) [0x00007FF6109FEF7F]
	(No symbol) [0x00007FF610A1EF82]
	(No symbol) [0x00007FF6109F6FC3]
	(No symbol) [0x00007FF6109BFEFE]
	(No symbol) [0x00007FF6109C1183]
	GetMachineCode [0x00007FF610EB048D+2461117]
	GetMachineCode [0x00007FF610F03B03+2802739]
	GetMachineCode [0x00007FF610EF953D+2760301]
	GetMachineCode [0x00007FF610C5ED0A+30266]
	(No symbol) [0x00007FF610AEEC4F]
	(No symbol) [0x00007FF610AEB214]
	(No symbol) [0x00007FF610AEB3B6]
	(No symbol) [0x00007FF610ADA1E9]
	BaseThreadInitThunk [0x00007FFFE630E8D7+23]
	RtlUserThreadStart [0x00007FFFE80BC34C+44]

2025-07-30 19:34:32 | ERROR    | video_uploader_unified:_take_screenshot:1289 - 保存截图失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x00007FF610B87EB5+84709]
	(No symbol) [0x00007FF610AE4200]
	(No symbol) [0x00007FF610978D7C]
	(No symbol) [0x00007FF6109BF11F]
	(No symbol) [0x00007FF6109F70B2]
	(No symbol) [0x00007FF6109F1A49]
	(No symbol) [0x00007FF6109F0AF9]
	(No symbol) [0x00007FF610945595]
	GetMachineCode [0x00007FF610EB048D+2461117]
	GetMachineCode [0x00007FF610F03B03+2802739]
	GetMachineCode [0x00007FF610EF953D+2760301]
	GetMachineCode [0x00007FF610C5ED0A+30266]
	(No symbol) [0x00007FF610AEEC4F]
	(No symbol) [0x00007FF6109441AE]
	GetMachineCode [0x00007FF610F760D8+3271176]
	BaseThreadInitThunk [0x00007FFFE630E8D7+23]
	RtlUserThreadStart [0x00007FFFE80BC34C+44]

2025-07-30 19:34:32 | ERROR    | video_uploader_unified:upload_video:345 - ❌ 导航到YouTube Studio失败
2025-07-30 19:34:32 | ERROR    | main:upload_single_video:404 - ❌ YouTube视频上传失败: ea293defa554c31911c1daca3b82c6a5.mp4
2025-07-30 19:34:32 | ERROR    | video_uploader_unified:_take_screenshot:1289 - 保存截图失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x00007FF610B87EB5+84709]
	(No symbol) [0x00007FF610AE4200]
	(No symbol) [0x00007FF610978D7C]
	(No symbol) [0x00007FF6109BF11F]
	(No symbol) [0x00007FF6109F70B2]
	(No symbol) [0x00007FF6109F1A49]
	(No symbol) [0x00007FF6109F0AF9]
	(No symbol) [0x00007FF610945595]
	GetMachineCode [0x00007FF610EB048D+2461117]
	GetMachineCode [0x00007FF610F03B03+2802739]
	GetMachineCode [0x00007FF610EF953D+2760301]
	GetMachineCode [0x00007FF610C5ED0A+30266]
	(No symbol) [0x00007FF610AEEC4F]
	(No symbol) [0x00007FF6109441AE]
	GetMachineCode [0x00007FF610F760D8+3271176]
	BaseThreadInitThunk [0x00007FFFE630E8D7+23]
	RtlUserThreadStart [0x00007FFFE80BC34C+44]

2025-07-30 19:34:32 | INFO     | main:upload_single_video:411 - 📸 错误截图已保存: error_1753875272.png
2025-07-30 19:34:35 | INFO     | main:cleanup:499 - 清理资源...
2025-07-30 19:34:38 | INFO     | video_uploader_unified:close:1510 - 统一视频上传器已关闭
2025-07-30 19:34:38 | INFO     | browser_manager:stop_browser_profile:377 - 关闭WebDriver连接
2025-07-30 19:34:54 | INFO     | browser_manager:stop_browser_profile:382 - 停止浏览器环境: 1285729718
2025-07-30 19:34:55 | WARNING  | browser_manager:stop_browser_profile:395 - 停止浏览器失败: 未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)
2025-07-30 19:34:55 | INFO     | main:cleanup:507 - 资源清理完成
